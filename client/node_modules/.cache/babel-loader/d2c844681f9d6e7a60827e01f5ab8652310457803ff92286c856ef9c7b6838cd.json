{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ArrowLeftRight = createLucideIcon(\"ArrowLeftRight\", [[\"path\", {\n  d: \"M8 3 4 7l4 4\",\n  key: \"9rb6wj\"\n}], [\"path\", {\n  d: \"M4 7h16\",\n  key: \"6tx8e3\"\n}], [\"path\", {\n  d: \"m16 21 4-4-4-4\",\n  key: \"siv7j2\"\n}], [\"path\", {\n  d: \"M20 17H4\",\n  key: \"h6l3hr\"\n}]]);\nexport { ArrowLeftRight as default };", "map": {"version": 3, "names": ["ArrowLeftRight", "createLucideIcon", "d", "key"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/arrow-left-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowLeftRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzIDQgN2w0IDQiIC8+CiAgPHBhdGggZD0iTTQgN2gxNiIgLz4KICA8cGF0aCBkPSJtMTYgMjEgNC00LTQtNCIgLz4KICA8cGF0aCBkPSJNMjAgMTdINCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeftRight = createLucideIcon('ArrowLeftRight', [\n  ['path', { d: 'M8 3 4 7l4 4', key: '9rb6wj' }],\n  ['path', { d: 'M4 7h16', key: '6tx8e3' }],\n  ['path', { d: 'm16 21 4-4-4-4', key: 'siv7j2' }],\n  ['path', { d: 'M20 17H4', key: 'h6l3hr' }],\n]);\n\nexport default ArrowLeftRight;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}