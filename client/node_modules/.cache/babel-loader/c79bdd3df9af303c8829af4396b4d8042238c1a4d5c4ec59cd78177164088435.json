{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nexport var AlphaPointer = function AlphaPointer(_ref) {\n  var direction = _ref.direction;\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        width: '18px',\n        height: '18px',\n        borderRadius: '50%',\n        transform: 'translate(-9px, -1px)',\n        backgroundColor: 'rgb(248, 248, 248)',\n        boxShadow: '0 1px 4px 0 rgba(0, 0, 0, 0.37)'\n      }\n    },\n    'vertical': {\n      picker: {\n        transform: 'translate(-3px, -9px)'\n      }\n    }\n  }, {\n    vertical: direction === 'vertical'\n  });\n  return React.createElement('div', {\n    style: styles.picker\n  });\n};\nexport default AlphaPointer;", "map": {"version": 3, "names": ["React", "reactCSS", "AlphaPointer", "_ref", "direction", "styles", "picker", "width", "height", "borderRadius", "transform", "backgroundColor", "boxShadow", "vertical", "createElement", "style"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/alpha/AlphaPointer.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\n\nexport var AlphaPointer = function AlphaPointer(_ref) {\n  var direction = _ref.direction;\n\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        width: '18px',\n        height: '18px',\n        borderRadius: '50%',\n        transform: 'translate(-9px, -1px)',\n        backgroundColor: 'rgb(248, 248, 248)',\n        boxShadow: '0 1px 4px 0 rgba(0, 0, 0, 0.37)'\n      }\n    },\n    'vertical': {\n      picker: {\n        transform: 'translate(-3px, -9px)'\n      }\n    }\n  }, { vertical: direction === 'vertical' });\n\n  return React.createElement('div', { style: styles.picker });\n};\n\nexport default AlphaPointer;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAE/B,OAAO,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EACpD,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;EAE9B,IAAIC,MAAM,GAAGJ,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTK,MAAM,EAAE;QACNC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAE,oBAAoB;QACrCC,SAAS,EAAE;MACb;IACF,CAAC;IACD,UAAU,EAAE;MACVN,MAAM,EAAE;QACNI,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAE;IAAEG,QAAQ,EAAET,SAAS,KAAK;EAAW,CAAC,CAAC;EAE1C,OAAOJ,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEV,MAAM,CAACC;EAAO,CAAC,CAAC;AAC7D,CAAC;AAED,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}