{"ast": null, "code": "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\");\n  }\n  return shallow$1(objA, objB);\n};\nexport { shallow as default, shallow$1 as shallow };", "map": {"version": 3, "names": ["shallow$1", "objA", "objB", "Object", "is", "Map", "size", "key", "value", "get", "Set", "has", "keysA", "keys", "length", "keyA", "prototype", "hasOwnProperty", "call", "shallow", "import", "meta", "env", "MODE", "console", "warn", "default"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/zustand/esm/shallow.mjs"], "sourcesContent": ["function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC7B,IAAIC,MAAM,CAACC,EAAE,CAACH,IAAI,EAAEC,IAAI,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1F,OAAO,KAAK;EACd;EACA,IAAID,IAAI,YAAYI,GAAG,IAAIH,IAAI,YAAYG,GAAG,EAAE;IAC9C,IAAIJ,IAAI,CAACK,IAAI,KAAKJ,IAAI,CAACI,IAAI,EAAE,OAAO,KAAK;IACzC,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIP,IAAI,EAAE;MAC/B,IAAI,CAACE,MAAM,CAACC,EAAE,CAACI,KAAK,EAAEN,IAAI,CAACO,GAAG,CAACF,GAAG,CAAC,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,IAAIN,IAAI,YAAYS,GAAG,IAAIR,IAAI,YAAYQ,GAAG,EAAE;IAC9C,IAAIT,IAAI,CAACK,IAAI,KAAKJ,IAAI,CAACI,IAAI,EAAE,OAAO,KAAK;IACzC,KAAK,MAAME,KAAK,IAAIP,IAAI,EAAE;MACxB,IAAI,CAACC,IAAI,CAACS,GAAG,CAACH,KAAK,CAAC,EAAE;QACpB,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,MAAMI,KAAK,GAAGT,MAAM,CAACU,IAAI,CAACZ,IAAI,CAAC;EAC/B,IAAIW,KAAK,CAACE,MAAM,KAAKX,MAAM,CAACU,IAAI,CAACX,IAAI,CAAC,CAACY,MAAM,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,KAAK,MAAMC,IAAI,IAAIH,KAAK,EAAE;IACxB,IAAI,CAACT,MAAM,CAACa,SAAS,CAACC,cAAc,CAACC,IAAI,CAAChB,IAAI,EAAEa,IAAI,CAAC,IAAI,CAACZ,MAAM,CAACC,EAAE,CAACH,IAAI,CAACc,IAAI,CAAC,EAAEb,IAAI,CAACa,IAAI,CAAC,CAAC,EAAE;MAC3F,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,IAAII,OAAO,GAAGA,CAAClB,IAAI,EAAEC,IAAI,KAAK;EAC5B,IAAI,CAACkB,MAAM,CAACC,IAAI,CAACC,GAAG,GAAGF,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,EAAE;IACtEC,OAAO,CAACC,IAAI,CACV,qGACF,CAAC;EACH;EACA,OAAOzB,SAAS,CAACC,IAAI,EAAEC,IAAI,CAAC;AAC9B,CAAC;AAED,SAASiB,OAAO,IAAIO,OAAO,EAAE1B,SAAS,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}