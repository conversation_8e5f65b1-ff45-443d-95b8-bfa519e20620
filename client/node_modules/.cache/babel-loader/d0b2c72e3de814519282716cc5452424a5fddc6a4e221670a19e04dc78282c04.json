{"ast": null, "code": "var _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nimport React from 'react';\nimport reactCSS from 'reactcss';\nimport { handleFocus } from '../../helpers/interaction';\nimport Checkboard from './Checkboard';\nvar ENTER = 13;\nexport var Swatch = function Swatch(_ref) {\n  var color = _ref.color,\n    style = _ref.style,\n    _ref$onClick = _ref.onClick,\n    onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n    onHover = _ref.onHover,\n    _ref$title = _ref.title,\n    title = _ref$title === undefined ? color : _ref$title,\n    children = _ref.children,\n    focus = _ref.focus,\n    _ref$focusStyle = _ref.focusStyle,\n    focusStyle = _ref$focusStyle === undefined ? {} : _ref$focusStyle;\n  var transparent = color === 'transparent';\n  var styles = reactCSS({\n    default: {\n      swatch: _extends({\n        background: color,\n        height: '100%',\n        width: '100%',\n        cursor: 'pointer',\n        position: 'relative',\n        outline: 'none'\n      }, style, focus ? focusStyle : {})\n    }\n  });\n  var handleClick = function handleClick(e) {\n    return onClick(color, e);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    return e.keyCode === ENTER && onClick(color, e);\n  };\n  var handleHover = function handleHover(e) {\n    return onHover(color, e);\n  };\n  var optionalEvents = {};\n  if (onHover) {\n    optionalEvents.onMouseOver = handleHover;\n  }\n  return React.createElement('div', _extends({\n    style: styles.swatch,\n    onClick: handleClick,\n    title: title,\n    tabIndex: 0,\n    onKeyDown: handleKeyDown\n  }, optionalEvents), children, transparent && React.createElement(Checkboard, {\n    borderRadius: styles.swatch.borderRadius,\n    boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.1)'\n  }));\n};\nexport default handleFocus(Swatch);", "map": {"version": 3, "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "React", "reactCSS", "handleFocus", "Checkboard", "ENTER", "Swatch", "_ref", "color", "style", "_ref$onClick", "onClick", "undefined", "onHover", "_ref$title", "title", "children", "focus", "_ref$focusStyle", "focusStyle", "transparent", "styles", "default", "swatch", "background", "height", "width", "cursor", "position", "outline", "handleClick", "e", "handleKeyDown", "keyCode", "handleHover", "optionalEvents", "onMouseOver", "createElement", "tabIndex", "onKeyDown", "borderRadius", "boxShadow"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/common/Swatch.js"], "sourcesContent": ["var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nimport React from 'react';\nimport reactCSS from 'reactcss';\nimport { handleFocus } from '../../helpers/interaction';\n\nimport Checkboard from './Checkboard';\n\nvar ENTER = 13;\n\nexport var Swatch = function Swatch(_ref) {\n  var color = _ref.color,\n      style = _ref.style,\n      _ref$onClick = _ref.onClick,\n      onClick = _ref$onClick === undefined ? function () {} : _ref$onClick,\n      onHover = _ref.onHover,\n      _ref$title = _ref.title,\n      title = _ref$title === undefined ? color : _ref$title,\n      children = _ref.children,\n      focus = _ref.focus,\n      _ref$focusStyle = _ref.focusStyle,\n      focusStyle = _ref$focusStyle === undefined ? {} : _ref$focusStyle;\n\n  var transparent = color === 'transparent';\n  var styles = reactCSS({\n    default: {\n      swatch: _extends({\n        background: color,\n        height: '100%',\n        width: '100%',\n        cursor: 'pointer',\n        position: 'relative',\n        outline: 'none'\n      }, style, focus ? focusStyle : {})\n    }\n  });\n\n  var handleClick = function handleClick(e) {\n    return onClick(color, e);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    return e.keyCode === ENTER && onClick(color, e);\n  };\n  var handleHover = function handleHover(e) {\n    return onHover(color, e);\n  };\n\n  var optionalEvents = {};\n  if (onHover) {\n    optionalEvents.onMouseOver = handleHover;\n  }\n\n  return React.createElement(\n    'div',\n    _extends({\n      style: styles.swatch,\n      onClick: handleClick,\n      title: title,\n      tabIndex: 0,\n      onKeyDown: handleKeyDown\n    }, optionalEvents),\n    children,\n    transparent && React.createElement(Checkboard, {\n      borderRadius: styles.swatch.borderRadius,\n      boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.1)'\n    })\n  );\n};\n\nexport default handleFocus(Swatch);"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIN,MAAM,CAACQ,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQ,OAAOS,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,SAASC,WAAW,QAAQ,2BAA2B;AAEvD,OAAOC,UAAU,MAAM,cAAc;AAErC,IAAIC,KAAK,GAAG,EAAE;AAEd,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,YAAY,GAAGH,IAAI,CAACI,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAKE,SAAS,GAAG,YAAY,CAAC,CAAC,GAAGF,YAAY;IACpEG,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,UAAU,GAAGP,IAAI,CAACQ,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAKF,SAAS,GAAGJ,KAAK,GAAGM,UAAU;IACrDE,QAAQ,GAAGT,IAAI,CAACS,QAAQ;IACxBC,KAAK,GAAGV,IAAI,CAACU,KAAK;IAClBC,eAAe,GAAGX,IAAI,CAACY,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAKN,SAAS,GAAG,CAAC,CAAC,GAAGM,eAAe;EAErE,IAAIE,WAAW,GAAGZ,KAAK,KAAK,aAAa;EACzC,IAAIa,MAAM,GAAGnB,QAAQ,CAAC;IACpBoB,OAAO,EAAE;MACPC,MAAM,EAAElC,QAAQ,CAAC;QACfmC,UAAU,EAAEhB,KAAK;QACjBiB,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE;MACX,CAAC,EAAEpB,KAAK,EAAEQ,KAAK,GAAGE,UAAU,GAAG,CAAC,CAAC;IACnC;EACF,CAAC,CAAC;EAEF,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAE;IACxC,OAAOpB,OAAO,CAACH,KAAK,EAAEuB,CAAC,CAAC;EAC1B,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACD,CAAC,EAAE;IAC5C,OAAOA,CAAC,CAACE,OAAO,KAAK5B,KAAK,IAAIM,OAAO,CAACH,KAAK,EAAEuB,CAAC,CAAC;EACjD,CAAC;EACD,IAAIG,WAAW,GAAG,SAASA,WAAWA,CAACH,CAAC,EAAE;IACxC,OAAOlB,OAAO,CAACL,KAAK,EAAEuB,CAAC,CAAC;EAC1B,CAAC;EAED,IAAII,cAAc,GAAG,CAAC,CAAC;EACvB,IAAItB,OAAO,EAAE;IACXsB,cAAc,CAACC,WAAW,GAAGF,WAAW;EAC1C;EAEA,OAAOjC,KAAK,CAACoC,aAAa,CACxB,KAAK,EACLhD,QAAQ,CAAC;IACPoB,KAAK,EAAEY,MAAM,CAACE,MAAM;IACpBZ,OAAO,EAAEmB,WAAW;IACpBf,KAAK,EAAEA,KAAK;IACZuB,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAEP;EACb,CAAC,EAAEG,cAAc,CAAC,EAClBnB,QAAQ,EACRI,WAAW,IAAInB,KAAK,CAACoC,aAAa,CAACjC,UAAU,EAAE;IAC7CoC,YAAY,EAAEnB,MAAM,CAACE,MAAM,CAACiB,YAAY;IACxCC,SAAS,EAAE;EACb,CAAC,CACH,CAAC;AACH,CAAC;AAED,eAAetC,WAAW,CAACG,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}