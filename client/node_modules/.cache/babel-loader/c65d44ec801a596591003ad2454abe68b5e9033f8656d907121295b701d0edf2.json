{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileMusic = createLucideIcon(\"FileMusic\", [[\"circle\", {\n  cx: \"14\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1bzzi3\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1fncim\"\n}], [\"path\", {\n  d: \"M4 12.4V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2h-7.5\",\n  key: \"skc018\"\n}], [\"path\", {\n  d: \"M8 18v-7.7L16 9v7\",\n  key: \"1oie6o\"\n}]]);\nexport { FileMusic as default };", "map": {"version": 3, "names": ["FileMusic", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/file-music.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileMusic\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxNCIgY3k9IjE2IiByPSIyIiAvPgogIDxjaXJjbGUgY3g9IjYiIGN5PSIxOCIgcj0iMiIgLz4KICA8cGF0aCBkPSJNNCAxMi40VjRhMiAyIDAgMCAxIDItMmg4LjVMMjAgNy41VjIwYTIgMiAwIDAgMS0yIDJoLTcuNSIgLz4KICA8cGF0aCBkPSJNOCAxOHYtNy43TDE2IDl2NyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-music\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileMusic = createLucideIcon('FileMusic', [\n  ['circle', { cx: '14', cy: '16', r: '2', key: '1bzzi3' }],\n  ['circle', { cx: '6', cy: '18', r: '2', key: '1fncim' }],\n  ['path', { d: 'M4 12.4V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2h-7.5', key: 'skc018' }],\n  ['path', { d: 'M8 18v-7.7L16 9v7', key: '1oie6o' }],\n]);\n\nexport default FileMusic;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEH,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0DAA4D;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAEC,CAAA,EAAG,mBAAqB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}