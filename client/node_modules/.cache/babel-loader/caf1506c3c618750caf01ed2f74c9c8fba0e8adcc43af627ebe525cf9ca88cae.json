{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/vlsi-workflow/client/src/components/sidebar/Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';\nimport { ChevronLeft, ChevronRight, Layers, Settings, Users, Activity } from 'lucide-react';\nimport NodePalette from './NodePalette';\nimport PropertyPanel from './PropertyPanel';\nimport LayersPanel from './LayersPanel';\nimport CollaborationPanel from './CollaborationPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  selectedNode,\n  selectedEdge,\n  onUpdateNode,\n  onUpdateEdge,\n  isCollapsed,\n  onToggleCollapse\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState('palette');\n  const tabs = [{\n    id: 'palette',\n    label: 'Palette',\n    icon: Layers\n  }, {\n    id: 'properties',\n    label: 'Properties',\n    icon: Settings\n  }, {\n    id: 'layers',\n    label: 'Layers',\n    icon: Activity\n  }, {\n    id: 'collaboration',\n    label: 'Users',\n    icon: Users\n  }];\n  if (isCollapsed) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-panel w-12 flex flex-col items-center py-4 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onToggleCollapse,\n        className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n        title: \"Expand sidebar\",\n        children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), tabs.map(tab => {\n        const Icon = tab.icon;\n        return /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setActiveTab(tab.id);\n            onToggleCollapse();\n          },\n          className: `p-2 rounded-lg transition-colors ${activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'}`,\n          title: tab.label,\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)\n        }, tab.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar-panel w-80 flex flex-col h-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: tabs.map(tab => {\n          const Icon = tab.icon;\n          return /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab.id),\n            className: `flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: tab.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onToggleCollapse,\n        className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n        title: \"Collapse sidebar\",\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4\",\n      children: [activeTab === 'palette' && /*#__PURE__*/_jsxDEV(NodePalette, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 37\n      }, this), activeTab === 'properties' && /*#__PURE__*/_jsxDEV(PropertyPanel, {\n        selectedNode: selectedNode,\n        selectedEdge: selectedEdge,\n        onUpdateNode: onUpdateNode,\n        onUpdateEdge: onUpdateEdge\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this), activeTab === 'layers' && /*#__PURE__*/_jsxDEV(LayersPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 36\n      }, this), activeTab === 'collaboration' && /*#__PURE__*/_jsxDEV(CollaborationPanel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 43\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"9t1WM/BcCrYK/Q6FganHCVVmBIk=\");\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "Panel", "PanelGroup", "PanelResizeHandle", "ChevronLeft", "ChevronRight", "Layers", "Settings", "Users", "Activity", "NodePalette", "PropertyPanel", "LayersPanel", "CollaborationPanel", "jsxDEV", "_jsxDEV", "Sidebar", "selectedNode", "selected<PERSON><PERSON>", "onUpdateNode", "onUpdateEdge", "isCollapsed", "onToggleCollapse", "_s", "activeTab", "setActiveTab", "tabs", "id", "label", "icon", "className", "children", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "tab", "Icon", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/vlsi-workflow/client/src/components/sidebar/Sidebar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';\nimport { \n  ChevronLeft, \n  ChevronRight, \n  Layers, \n  Settings,\n  Users,\n  Activity\n} from 'lucide-react';\nimport NodePalette from './NodePalette';\nimport PropertyPanel from './PropertyPanel';\nimport LayersPanel from './LayersPanel';\nimport CollaborationPanel from './CollaborationPanel';\n\nconst Sidebar = ({ \n  selectedNode, \n  selectedEdge, \n  onUpdateNode, \n  onUpdateEdge,\n  isCollapsed,\n  onToggleCollapse \n}) => {\n  const [activeTab, setActiveTab] = useState('palette');\n\n  const tabs = [\n    { id: 'palette', label: 'Palette', icon: Layers },\n    { id: 'properties', label: 'Properties', icon: Settings },\n    { id: 'layers', label: 'Layers', icon: Activity },\n    { id: 'collaboration', label: 'Users', icon: Users },\n  ];\n\n  if (isCollapsed) {\n    return (\n      <div className=\"sidebar-panel w-12 flex flex-col items-center py-4 space-y-2\">\n        <button\n          onClick={onToggleCollapse}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          title=\"Expand sidebar\"\n        >\n          <ChevronRight className=\"w-5 h-5 text-gray-600\" />\n        </button>\n        {tabs.map((tab) => {\n          const Icon = tab.icon;\n          return (\n            <button\n              key={tab.id}\n              onClick={() => {\n                setActiveTab(tab.id);\n                onToggleCollapse();\n              }}\n              className={`p-2 rounded-lg transition-colors ${\n                activeTab === tab.id\n                  ? 'bg-blue-100 text-blue-600'\n                  : 'hover:bg-gray-100 text-gray-600'\n              }`}\n              title={tab.label}\n            >\n              <Icon className=\"w-5 h-5\" />\n            </button>\n          );\n        })}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"sidebar-panel w-80 flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-4\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${\n                  activeTab === tab.id\n                    ? 'bg-blue-100 text-blue-600'\n                    : 'hover:bg-gray-100 text-gray-600'\n                }`}\n              >\n                <Icon className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">{tab.label}</span>\n              </button>\n            );\n          })}\n        </div>\n        <button\n          onClick={onToggleCollapse}\n          className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          title=\"Collapse sidebar\"\n        >\n          <ChevronLeft className=\"w-5 h-5 text-gray-600\" />\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4\">\n        {activeTab === 'palette' && <NodePalette />}\n        {activeTab === 'properties' && (\n          <PropertyPanel\n            selectedNode={selectedNode}\n            selectedEdge={selectedEdge}\n            onUpdateNode={onUpdateNode}\n            onUpdateEdge={onUpdateEdge}\n          />\n        )}\n        {activeTab === 'layers' && <LayersPanel />}\n        {activeTab === 'collaboration' && <CollaborationPanel />}\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,KAAK,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,wBAAwB;AAC7E,SACEC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,QAAQ,QACH,cAAc;AACrB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,OAAO,GAAGA,CAAC;EACfC,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC,YAAY;EACZC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,SAAS,CAAC;EAErD,MAAM0B,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEvB;EAAO,CAAC,EACjD;IAAEqB,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,IAAI,EAAEtB;EAAS,CAAC,EACzD;IAAEoB,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAEpB;EAAS,CAAC,EACjD;IAAEkB,EAAE,EAAE,eAAe;IAAEC,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAErB;EAAM,CAAC,CACrD;EAED,IAAIa,WAAW,EAAE;IACf,oBACEN,OAAA;MAAKe,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBAC3EhB,OAAA;QACEiB,OAAO,EAAEV,gBAAiB;QAC1BQ,SAAS,EAAC,oDAAoD;QAC9DG,KAAK,EAAC,gBAAgB;QAAAF,QAAA,eAEtBhB,OAAA,CAACV,YAAY;UAACyB,SAAS,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,EACRX,IAAI,CAACY,GAAG,CAAEC,GAAG,IAAK;QACjB,MAAMC,IAAI,GAAGD,GAAG,CAACV,IAAI;QACrB,oBACEd,OAAA;UAEEiB,OAAO,EAAEA,CAAA,KAAM;YACbP,YAAY,CAACc,GAAG,CAACZ,EAAE,CAAC;YACpBL,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFQ,SAAS,EAAE,oCACTN,SAAS,KAAKe,GAAG,CAACZ,EAAE,GAChB,2BAA2B,GAC3B,iCAAiC,EACpC;UACHM,KAAK,EAAEM,GAAG,CAACX,KAAM;UAAAG,QAAA,eAEjBhB,OAAA,CAACyB,IAAI;YAACV,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAZvBE,GAAG,CAACZ,EAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaL,CAAC;MAEb,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEtB,OAAA;IAAKe,SAAS,EAAC,yCAAyC;IAAAC,QAAA,gBAEtDhB,OAAA;MAAKe,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC7EhB,OAAA;QAAKe,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EACzCL,IAAI,CAACY,GAAG,CAAEC,GAAG,IAAK;UACjB,MAAMC,IAAI,GAAGD,GAAG,CAACV,IAAI;UACrB,oBACEd,OAAA;YAEEiB,OAAO,EAAEA,CAAA,KAAMP,YAAY,CAACc,GAAG,CAACZ,EAAE,CAAE;YACpCG,SAAS,EAAE,sEACTN,SAAS,KAAKe,GAAG,CAACZ,EAAE,GAChB,2BAA2B,GAC3B,iCAAiC,EACpC;YAAAI,QAAA,gBAEHhB,OAAA,CAACyB,IAAI;cAACV,SAAS,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BtB,OAAA;cAAMe,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEQ,GAAG,CAACX;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATnDE,GAAG,CAACZ,EAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUL,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtB,OAAA;QACEiB,OAAO,EAAEV,gBAAiB;QAC1BQ,SAAS,EAAC,oDAAoD;QAC9DG,KAAK,EAAC,kBAAkB;QAAAF,QAAA,eAExBhB,OAAA,CAACX,WAAW;UAAC0B,SAAS,EAAC;QAAuB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtB,OAAA;MAAKe,SAAS,EAAC,4BAA4B;MAAAC,QAAA,GACxCP,SAAS,KAAK,SAAS,iBAAIT,OAAA,CAACL,WAAW;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1Cb,SAAS,KAAK,YAAY,iBACzBT,OAAA,CAACJ,aAAa;QACZM,YAAY,EAAEA,YAAa;QAC3BC,YAAY,EAAEA,YAAa;QAC3BC,YAAY,EAAEA,YAAa;QAC3BC,YAAY,EAAEA;MAAa;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACF,EACAb,SAAS,KAAK,QAAQ,iBAAIT,OAAA,CAACH,WAAW;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACzCb,SAAS,KAAK,eAAe,iBAAIT,OAAA,CAACF,kBAAkB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAnGIP,OAAO;AAAAyB,EAAA,GAAPzB,OAAO;AAqGb,eAAeA,OAAO;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}