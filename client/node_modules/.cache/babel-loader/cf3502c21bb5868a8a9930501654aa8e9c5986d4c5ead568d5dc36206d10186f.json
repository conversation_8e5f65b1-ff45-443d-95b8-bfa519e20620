{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/vlsi-workflow/client/src/contexts/SocketContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport io from 'socket.io-client';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SocketContext = /*#__PURE__*/createContext();\nexport const useSocket = () => {\n  _s();\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n_s(useSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SocketProvider = ({\n  children\n}) => {\n  _s2();\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [activeUsers, setActiveUsers] = useState([]);\n  useEffect(() => {\n    const newSocket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5001');\n    newSocket.on('connect', () => {\n      setConnected(true);\n      console.log('Connected to server');\n    });\n    newSocket.on('disconnect', () => {\n      setConnected(false);\n      console.log('Disconnected from server');\n    });\n    newSocket.on('userJoined', data => {\n      setActiveUsers(data.activeUsers);\n    });\n    newSocket.on('userLeft', data => {\n      setActiveUsers(data.activeUsers);\n    });\n    setSocket(newSocket);\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n  const joinWorkflow = workflowId => {\n    if (socket) {\n      socket.emit('joinWorkflow', workflowId);\n    }\n  };\n  const leaveWorkflow = workflowId => {\n    if (socket) {\n      socket.emit('leaveWorkflow', workflowId);\n    }\n  };\n  const emitNodeUpdate = (workflowId, nodeData) => {\n    if (socket) {\n      socket.emit('nodeUpdate', {\n        workflowId,\n        ...nodeData\n      });\n    }\n  };\n  const emitEdgeUpdate = (workflowId, edgeData) => {\n    if (socket) {\n      socket.emit('edgeUpdate', {\n        workflowId,\n        ...edgeData\n      });\n    }\n  };\n  const emitCursorMove = (workflowId, position) => {\n    if (socket) {\n      socket.emit('cursorMove', {\n        workflowId,\n        position\n      });\n    }\n  };\n  const value = {\n    socket,\n    connected,\n    activeUsers,\n    joinWorkflow,\n    leaveWorkflow,\n    emitNodeUpdate,\n    emitEdgeUpdate,\n    emitCursorMove\n  };\n  return /*#__PURE__*/_jsxDEV(SocketContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s2(SocketProvider, \"qYWBdqFo8RIq8pmmBNCW3WlJUp8=\");\n_c = SocketProvider;\nvar _c;\n$RefreshReg$(_c, \"SocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "jsxDEV", "_jsxDEV", "SocketContext", "useSocket", "_s", "context", "Error", "SocketProvider", "children", "_s2", "socket", "setSocket", "connected", "setConnected", "activeUsers", "setActiveUsers", "newSocket", "process", "env", "REACT_APP_SERVER_URL", "on", "console", "log", "data", "close", "joinWorkflow", "workflowId", "emit", "leaveWorkflow", "emitNodeUpdate", "nodeData", "emitEdgeUpdate", "edgeData", "emitCursorMove", "position", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/vlsi-workflow/client/src/contexts/SocketContext.js"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport io from 'socket.io-client';\n\nconst SocketContext = createContext();\n\nexport const useSocket = () => {\n  const context = useContext(SocketContext);\n  if (!context) {\n    throw new Error('useSocket must be used within a SocketProvider');\n  }\n  return context;\n};\n\nexport const SocketProvider = ({ children }) => {\n  const [socket, setSocket] = useState(null);\n  const [connected, setConnected] = useState(false);\n  const [activeUsers, setActiveUsers] = useState([]);\n\n  useEffect(() => {\n    const newSocket = io(process.env.REACT_APP_SERVER_URL || 'http://localhost:5001');\n    \n    newSocket.on('connect', () => {\n      setConnected(true);\n      console.log('Connected to server');\n    });\n\n    newSocket.on('disconnect', () => {\n      setConnected(false);\n      console.log('Disconnected from server');\n    });\n\n    newSocket.on('userJoined', (data) => {\n      setActiveUsers(data.activeUsers);\n    });\n\n    newSocket.on('userLeft', (data) => {\n      setActiveUsers(data.activeUsers);\n    });\n\n    setSocket(newSocket);\n\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n\n  const joinWorkflow = (workflowId) => {\n    if (socket) {\n      socket.emit('joinWorkflow', workflowId);\n    }\n  };\n\n  const leaveWorkflow = (workflowId) => {\n    if (socket) {\n      socket.emit('leaveWorkflow', workflowId);\n    }\n  };\n\n  const emitNodeUpdate = (workflowId, nodeData) => {\n    if (socket) {\n      socket.emit('nodeUpdate', { workflowId, ...nodeData });\n    }\n  };\n\n  const emitEdgeUpdate = (workflowId, edgeData) => {\n    if (socket) {\n      socket.emit('edgeUpdate', { workflowId, ...edgeData });\n    }\n  };\n\n  const emitCursorMove = (workflowId, position) => {\n    if (socket) {\n      socket.emit('cursorMove', { workflowId, position });\n    }\n  };\n\n  const value = {\n    socket,\n    connected,\n    activeUsers,\n    joinWorkflow,\n    leaveWorkflow,\n    emitNodeUpdate,\n    emitEdgeUpdate,\n    emitCursorMove,\n  };\n\n  return (\n    <SocketContext.Provider value={value}>\n      {children}\n    </SocketContext.Provider>\n  );\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,gBAAGP,aAAa,CAAC,CAAC;AAErC,OAAO,MAAMQ,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,OAAO,GAAGT,UAAU,CAACM,aAAa,CAAC;EACzC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,SAAS;AAQtB,OAAO,MAAMI,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAElDD,SAAS,CAAC,MAAM;IACd,MAAMmB,SAAS,GAAGjB,EAAE,CAACkB,OAAO,CAACC,GAAG,CAACC,oBAAoB,IAAI,uBAAuB,CAAC;IAEjFH,SAAS,CAACI,EAAE,CAAC,SAAS,EAAE,MAAM;MAC5BP,YAAY,CAAC,IAAI,CAAC;MAClBQ,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,CAAC;IAEFN,SAAS,CAACI,EAAE,CAAC,YAAY,EAAE,MAAM;MAC/BP,YAAY,CAAC,KAAK,CAAC;MACnBQ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC;IAEFN,SAAS,CAACI,EAAE,CAAC,YAAY,EAAGG,IAAI,IAAK;MACnCR,cAAc,CAACQ,IAAI,CAACT,WAAW,CAAC;IAClC,CAAC,CAAC;IAEFE,SAAS,CAACI,EAAE,CAAC,UAAU,EAAGG,IAAI,IAAK;MACjCR,cAAc,CAACQ,IAAI,CAACT,WAAW,CAAC;IAClC,CAAC,CAAC;IAEFH,SAAS,CAACK,SAAS,CAAC;IAEpB,OAAO,MAAM;MACXA,SAAS,CAACQ,KAAK,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACnC,IAAIhB,MAAM,EAAE;MACVA,MAAM,CAACiB,IAAI,CAAC,cAAc,EAAED,UAAU,CAAC;IACzC;EACF,CAAC;EAED,MAAME,aAAa,GAAIF,UAAU,IAAK;IACpC,IAAIhB,MAAM,EAAE;MACVA,MAAM,CAACiB,IAAI,CAAC,eAAe,EAAED,UAAU,CAAC;IAC1C;EACF,CAAC;EAED,MAAMG,cAAc,GAAGA,CAACH,UAAU,EAAEI,QAAQ,KAAK;IAC/C,IAAIpB,MAAM,EAAE;MACVA,MAAM,CAACiB,IAAI,CAAC,YAAY,EAAE;QAAED,UAAU;QAAE,GAAGI;MAAS,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACL,UAAU,EAAEM,QAAQ,KAAK;IAC/C,IAAItB,MAAM,EAAE;MACVA,MAAM,CAACiB,IAAI,CAAC,YAAY,EAAE;QAAED,UAAU;QAAE,GAAGM;MAAS,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACP,UAAU,EAAEQ,QAAQ,KAAK;IAC/C,IAAIxB,MAAM,EAAE;MACVA,MAAM,CAACiB,IAAI,CAAC,YAAY,EAAE;QAAED,UAAU;QAAEQ;MAAS,CAAC,CAAC;IACrD;EACF,CAAC;EAED,MAAMC,KAAK,GAAG;IACZzB,MAAM;IACNE,SAAS;IACTE,WAAW;IACXW,YAAY;IACZG,aAAa;IACbC,cAAc;IACdE,cAAc;IACdE;EACF,CAAC;EAED,oBACEhC,OAAA,CAACC,aAAa,CAACkC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3B,QAAA,EAClCA;EAAQ;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAE7B,CAAC;AAAC/B,GAAA,CA/EWF,cAAc;AAAAkC,EAAA,GAAdlC,cAAc;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}