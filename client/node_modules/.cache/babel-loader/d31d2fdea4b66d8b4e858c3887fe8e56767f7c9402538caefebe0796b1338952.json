{"ast": null, "code": "export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x,\n  y,\n  dx,\n  dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {\n      value: type,\n      enumerable: true,\n      configurable: true\n    },\n    sourceEvent: {\n      value: sourceEvent,\n      enumerable: true,\n      configurable: true\n    },\n    subject: {\n      value: subject,\n      enumerable: true,\n      configurable: true\n    },\n    target: {\n      value: target,\n      enumerable: true,\n      configurable: true\n    },\n    identifier: {\n      value: identifier,\n      enumerable: true,\n      configurable: true\n    },\n    active: {\n      value: active,\n      enumerable: true,\n      configurable: true\n    },\n    x: {\n      value: x,\n      enumerable: true,\n      configurable: true\n    },\n    y: {\n      value: y,\n      enumerable: true,\n      configurable: true\n    },\n    dx: {\n      value: dx,\n      enumerable: true,\n      configurable: true\n    },\n    dy: {\n      value: dy,\n      enumerable: true,\n      configurable: true\n    },\n    _: {\n      value: dispatch\n    }\n  });\n}\nDragEvent.prototype.on = function () {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};", "map": {"version": 3, "names": ["DragEvent", "type", "sourceEvent", "subject", "target", "identifier", "active", "x", "y", "dx", "dy", "dispatch", "Object", "defineProperties", "value", "enumerable", "configurable", "_", "prototype", "on", "apply", "arguments"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/d3-drag/src/event.js"], "sourcesContent": ["export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,IAAI,EAAE;EACtCC,WAAW;EACXC,OAAO;EACPC,MAAM;EACNC,UAAU;EACVC,MAAM;EACNC,CAAC;EAAEC,CAAC;EAAEC,EAAE;EAAEC,EAAE;EACZC;AACF,CAAC,EAAE;EACDC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;IAC5BZ,IAAI,EAAE;MAACa,KAAK,EAAEb,IAAI;MAAEc,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACzDd,WAAW,EAAE;MAACY,KAAK,EAAEZ,WAAW;MAAEa,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACvEb,OAAO,EAAE;MAACW,KAAK,EAAEX,OAAO;MAAEY,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IAC/DZ,MAAM,EAAE;MAACU,KAAK,EAAEV,MAAM;MAAEW,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IAC7DX,UAAU,EAAE;MAACS,KAAK,EAAET,UAAU;MAAEU,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACrEV,MAAM,EAAE;MAACQ,KAAK,EAAER,MAAM;MAAES,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IAC7DT,CAAC,EAAE;MAACO,KAAK,EAAEP,CAAC;MAAEQ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACnDR,CAAC,EAAE;MAACM,KAAK,EAAEN,CAAC;MAAEO,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACnDP,EAAE,EAAE;MAACK,KAAK,EAAEL,EAAE;MAAEM,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACrDN,EAAE,EAAE;MAACI,KAAK,EAAEJ,EAAE;MAAEK,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACrDC,CAAC,EAAE;MAACH,KAAK,EAAEH;IAAQ;EACrB,CAAC,CAAC;AACJ;AAEAX,SAAS,CAACkB,SAAS,CAACC,EAAE,GAAG,YAAW;EAClC,IAAIL,KAAK,GAAG,IAAI,CAACG,CAAC,CAACE,EAAE,CAACC,KAAK,CAAC,IAAI,CAACH,CAAC,EAAEI,SAAS,CAAC;EAC9C,OAAOP,KAAK,KAAK,IAAI,CAACG,CAAC,GAAG,IAAI,GAAGH,KAAK;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}