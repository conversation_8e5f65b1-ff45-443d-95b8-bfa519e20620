{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Settings, Palette, Type, Hash, ToggleLeft, ToggleRight, Trash2, <PERSON><PERSON>, <PERSON>, EyeOff } from 'lucide-react';\nimport { ChromePicker } from 'react-color';\nimport { useWorkflow } from '../../contexts/WorkflowContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PropertyPanel = ({\n  selectedNode,\n  selectedEdge,\n  onUpdateNode,\n  onUpdateEdge\n}) => {\n  _s();\n  const {\n    deleteNode,\n    deleteEdge\n  } = useWorkflow();\n  const {\n    getCurrentTheme\n  } = useTheme();\n  const theme = getCurrentTheme();\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [localProperties, setLocalProperties] = useState({});\n  useEffect(() => {\n    if (selectedNode) {\n      setLocalProperties(selectedNode.data || {});\n    } else if (selectedEdge) {\n      setLocalProperties(selectedEdge.data || {});\n    } else {\n      setLocalProperties({});\n    }\n  }, [selectedNode, selectedEdge]);\n  const handlePropertyChange = (key, value) => {\n    const newProperties = {\n      ...localProperties,\n      [key]: value\n    };\n    setLocalProperties(newProperties);\n    if (selectedNode) {\n      onUpdateNode(selectedNode.id, {\n        data: newProperties\n      });\n    } else if (selectedEdge) {\n      onUpdateEdge(selectedEdge.id, {\n        data: newProperties\n      });\n    }\n  };\n  const handleDelete = () => {\n    if (selectedNode) {\n      deleteNode(selectedNode.id);\n    } else if (selectedEdge) {\n      deleteEdge(selectedEdge.id);\n    }\n  };\n  const handleDuplicate = () => {\n    if (selectedNode) {\n      const newNode = {\n        ...selectedNode,\n        id: `${selectedNode.id}_copy_${Date.now()}`,\n        position: {\n          x: selectedNode.position.x + 50,\n          y: selectedNode.position.y + 50\n        }\n      };\n      // This would need to be implemented in the workflow context\n      console.log('Duplicate node:', newNode);\n    }\n  };\n  const toggleVisibility = () => {\n    if (selectedNode) {\n      handlePropertyChange('hidden', !localProperties.hidden);\n    }\n  };\n  if (!selectedNode && !selectedEdge) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"property-panel\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-5 h-5\",\n          style: {\n            color: theme.colors.textSecondary\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold\",\n          style: {\n            color: theme.colors.textPrimary\n          },\n          children: \"Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8\",\n        style: {\n          color: theme.colors.textTertiary\n        },\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-12 h-12 mx-auto mb-3\",\n          style: {\n            color: theme.colors.textMuted\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a node or edge to view properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"property-panel space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Settings, {\n          className: \"w-5 h-5 text-gray-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Properties\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleVisibility,\n          className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n          title: localProperties.hidden ? 'Show' : 'Hide',\n          children: localProperties.hidden ? /*#__PURE__*/_jsxDEV(EyeOff, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleDuplicate,\n          className: \"p-1 hover:bg-gray-100 rounded transition-colors\",\n          title: \"Duplicate\",\n          children: /*#__PURE__*/_jsxDEV(Copy, {\n            className: \"w-4 h-4 text-gray-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleDelete,\n          className: \"p-1 hover:bg-red-100 rounded transition-colors\",\n          title: \"Delete\",\n          children: /*#__PURE__*/_jsxDEV(Trash2, {\n            className: \"w-4 h-4 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Type, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), \"Label\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: localProperties.label || '',\n          onChange: e => handlePropertyChange('label', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          placeholder: \"Enter label...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Description\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: localProperties.description || '',\n          onChange: e => handlePropertyChange('description', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          rows: \"3\",\n          placeholder: \"Enter description...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Palette, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), \"Color\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowColorPicker(!showColorPicker),\n            className: \"w-full h-10 border border-gray-300 rounded-md flex items-center px-3 space-x-2\",\n            style: {\n              backgroundColor: localProperties.color || '#ffffff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-6 rounded border border-gray-300\",\n              style: {\n                backgroundColor: localProperties.color || '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-700\",\n              children: localProperties.color || '#ffffff'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), showColorPicker && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-12 left-0 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fixed inset-0\",\n              onClick: () => setShowColorPicker(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ChromePicker, {\n              color: localProperties.color || '#ffffff',\n              onChange: color => handlePropertyChange('color', color.hex)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), selectedNode && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: \"Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: localProperties.status || 'pending',\n          onChange: e => handlePropertyChange('status', e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"pending\",\n            children: \"Pending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"running\",\n            children: \"Running\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"completed\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"failed\",\n            children: \"Failed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"paused\",\n            children: \"Paused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"warning\",\n            children: \"Warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), selectedNode && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Hash, {\n            className: \"w-4 h-4 inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), \"Progress (%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          min: \"0\",\n          max: \"100\",\n          value: localProperties.progress || 0,\n          onChange: e => handlePropertyChange('progress', parseInt(e.target.value)),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), selectedEdge && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-2\",\n            children: [localProperties.dashed ? /*#__PURE__*/_jsxDEV(ToggleRight, {\n              className: \"w-5 h-5 text-blue-500 cursor-pointer\",\n              onClick: () => handlePropertyChange('dashed', false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(ToggleLeft, {\n              className: \"w-5 h-5 text-gray-400 cursor-pointer\",\n              onClick: () => handlePropertyChange('dashed', true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Dashed Line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Line Width\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"range\",\n            min: \"1\",\n            max: \"10\",\n            value: localProperties.strokeWidth || 2,\n            onChange: e => handlePropertyChange('strokeWidth', parseInt(e.target.value)),\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [localProperties.strokeWidth || 2, \"px\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), localProperties.parameters && Object.keys(localProperties.parameters).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-700 mb-3\",\n        children: \"Parameters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: Object.entries(localProperties.parameters).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-600\",\n            children: key\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-mono text-gray-800\",\n            children: String(value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {\n        const key = prompt('Parameter name:');\n        const value = prompt('Parameter value:');\n        if (key && value) {\n          const newParams = {\n            ...localProperties.parameters,\n            [key]: value\n          };\n          handlePropertyChange('parameters', newParams);\n        }\n      },\n      className: \"w-full px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors\",\n      children: \"Add Parameter\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(PropertyPanel, \"fNcPqs+agLf3zWRzOjuicChstZk=\", false, function () {\n  return [useWorkflow, useTheme];\n});\n_c = PropertyPanel;\nexport default PropertyPanel;\nvar _c;\n$RefreshReg$(_c, \"PropertyPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Settings", "Palette", "Type", "Hash", "ToggleLeft", "ToggleRight", "Trash2", "Copy", "Eye", "Eye<PERSON>ff", "ChromePicker", "useWorkflow", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PropertyPanel", "selectedNode", "selected<PERSON><PERSON>", "onUpdateNode", "onUpdateEdge", "_s", "deleteNode", "deleteEdge", "getCurrentTheme", "theme", "showColorPicker", "setShowColorPicker", "localProperties", "setLocalProperties", "data", "handlePropertyChange", "key", "value", "newProperties", "id", "handleDelete", "handleDuplicate", "newNode", "Date", "now", "position", "x", "y", "console", "log", "toggleVisibility", "hidden", "className", "children", "style", "color", "colors", "textSecondary", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "textPrimary", "textTertiary", "textMuted", "onClick", "title", "type", "label", "onChange", "e", "target", "placeholder", "description", "rows", "backgroundColor", "hex", "status", "min", "max", "progress", "parseInt", "dashed", "strokeWidth", "parameters", "Object", "keys", "length", "entries", "map", "String", "prompt", "newParams", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Settings, \n  <PERSON><PERSON>, \n  <PERSON>, \n  Hash, \n  ToggleLeft, \n  ToggleRight,\n  Trash2,\n  <PERSON><PERSON>,\n  <PERSON>,\n  EyeOff\n} from 'lucide-react';\nimport { ChromePicker } from 'react-color';\nimport { useWorkflow } from '../../contexts/WorkflowContext';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst PropertyPanel = ({ selectedNode, selectedEdge, onUpdateNode, onUpdateEdge }) => {\n  const { deleteNode, deleteEdge } = useWorkflow();\n  const { getCurrentTheme } = useTheme();\n  const theme = getCurrentTheme();\n  const [showColorPicker, setShowColorPicker] = useState(false);\n  const [localProperties, setLocalProperties] = useState({});\n\n  useEffect(() => {\n    if (selectedNode) {\n      setLocalProperties(selectedNode.data || {});\n    } else if (selectedEdge) {\n      setLocalProperties(selectedEdge.data || {});\n    } else {\n      setLocalProperties({});\n    }\n  }, [selectedNode, selectedEdge]);\n\n  const handlePropertyChange = (key, value) => {\n    const newProperties = { ...localProperties, [key]: value };\n    setLocalProperties(newProperties);\n    \n    if (selectedNode) {\n      onUpdateNode(selectedNode.id, { data: newProperties });\n    } else if (selectedEdge) {\n      onUpdateEdge(selectedEdge.id, { data: newProperties });\n    }\n  };\n\n  const handleDelete = () => {\n    if (selectedNode) {\n      deleteNode(selectedNode.id);\n    } else if (selectedEdge) {\n      deleteEdge(selectedEdge.id);\n    }\n  };\n\n  const handleDuplicate = () => {\n    if (selectedNode) {\n      const newNode = {\n        ...selectedNode,\n        id: `${selectedNode.id}_copy_${Date.now()}`,\n        position: {\n          x: selectedNode.position.x + 50,\n          y: selectedNode.position.y + 50,\n        },\n      };\n      // This would need to be implemented in the workflow context\n      console.log('Duplicate node:', newNode);\n    }\n  };\n\n  const toggleVisibility = () => {\n    if (selectedNode) {\n      handlePropertyChange('hidden', !localProperties.hidden);\n    }\n  };\n\n  if (!selectedNode && !selectedEdge) {\n    return (\n      <div className=\"property-panel\">\n        <div className=\"flex items-center space-x-2 mb-4\">\n          <Settings className=\"w-5 h-5\" style={{ color: theme.colors.textSecondary }} />\n          <h2 className=\"text-lg font-semibold\" style={{ color: theme.colors.textPrimary }}>Properties</h2>\n        </div>\n        <div className=\"text-center py-8\" style={{ color: theme.colors.textTertiary }}>\n          <Settings className=\"w-12 h-12 mx-auto mb-3\" style={{ color: theme.colors.textMuted }} />\n          <p>Select a node or edge to view properties</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"property-panel space-y-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-2\">\n          <Settings className=\"w-5 h-5 text-gray-600\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">Properties</h2>\n        </div>\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={toggleVisibility}\n            className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n            title={localProperties.hidden ? 'Show' : 'Hide'}\n          >\n            {localProperties.hidden ? (\n              <EyeOff className=\"w-4 h-4 text-gray-500\" />\n            ) : (\n              <Eye className=\"w-4 h-4 text-gray-500\" />\n            )}\n          </button>\n          <button\n            onClick={handleDuplicate}\n            className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n            title=\"Duplicate\"\n          >\n            <Copy className=\"w-4 h-4 text-gray-500\" />\n          </button>\n          <button\n            onClick={handleDelete}\n            className=\"p-1 hover:bg-red-100 rounded transition-colors\"\n            title=\"Delete\"\n          >\n            <Trash2 className=\"w-4 h-4 text-red-500\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Basic Properties */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Type className=\"w-4 h-4 inline mr-1\" />\n            Label\n          </label>\n          <input\n            type=\"text\"\n            value={localProperties.label || ''}\n            onChange={(e) => handlePropertyChange('label', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"Enter label...\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Description\n          </label>\n          <textarea\n            value={localProperties.description || ''}\n            onChange={(e) => handlePropertyChange('description', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows=\"3\"\n            placeholder=\"Enter description...\"\n          />\n        </div>\n\n        {/* Color Picker */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <Palette className=\"w-4 h-4 inline mr-1\" />\n            Color\n          </label>\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowColorPicker(!showColorPicker)}\n              className=\"w-full h-10 border border-gray-300 rounded-md flex items-center px-3 space-x-2\"\n              style={{ backgroundColor: localProperties.color || '#ffffff' }}\n            >\n              <div \n                className=\"w-6 h-6 rounded border border-gray-300\"\n                style={{ backgroundColor: localProperties.color || '#ffffff' }}\n              />\n              <span className=\"text-sm text-gray-700\">\n                {localProperties.color || '#ffffff'}\n              </span>\n            </button>\n            {showColorPicker && (\n              <div className=\"absolute top-12 left-0 z-50\">\n                <div \n                  className=\"fixed inset-0\" \n                  onClick={() => setShowColorPicker(false)}\n                />\n                <ChromePicker\n                  color={localProperties.color || '#ffffff'}\n                  onChange={(color) => handlePropertyChange('color', color.hex)}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Status for nodes */}\n        {selectedNode && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Status\n            </label>\n            <select\n              value={localProperties.status || 'pending'}\n              onChange={(e) => handlePropertyChange('status', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"pending\">Pending</option>\n              <option value=\"running\">Running</option>\n              <option value=\"completed\">Completed</option>\n              <option value=\"failed\">Failed</option>\n              <option value=\"paused\">Paused</option>\n              <option value=\"warning\">Warning</option>\n            </select>\n          </div>\n        )}\n\n        {/* Progress for nodes */}\n        {selectedNode && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <Hash className=\"w-4 h-4 inline mr-1\" />\n              Progress (%)\n            </label>\n            <input\n              type=\"number\"\n              min=\"0\"\n              max=\"100\"\n              value={localProperties.progress || 0}\n              onChange={(e) => handlePropertyChange('progress', parseInt(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        )}\n\n        {/* Edge-specific properties */}\n        {selectedEdge && (\n          <>\n            <div>\n              <label className=\"flex items-center space-x-2\">\n                {localProperties.dashed ? (\n                  <ToggleRight \n                    className=\"w-5 h-5 text-blue-500 cursor-pointer\" \n                    onClick={() => handlePropertyChange('dashed', false)}\n                  />\n                ) : (\n                  <ToggleLeft \n                    className=\"w-5 h-5 text-gray-400 cursor-pointer\" \n                    onClick={() => handlePropertyChange('dashed', true)}\n                  />\n                )}\n                <span className=\"text-sm font-medium text-gray-700\">Dashed Line</span>\n              </label>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Line Width\n              </label>\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"10\"\n                value={localProperties.strokeWidth || 2}\n                onChange={(e) => handlePropertyChange('strokeWidth', parseInt(e.target.value))}\n                className=\"w-full\"\n              />\n              <div className=\"text-xs text-gray-500 mt-1\">\n                {localProperties.strokeWidth || 2}px\n              </div>\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Custom Parameters */}\n      {localProperties.parameters && Object.keys(localProperties.parameters).length > 0 && (\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Parameters</h3>\n          <div className=\"space-y-2\">\n            {Object.entries(localProperties.parameters).map(([key, value]) => (\n              <div key={key} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                <span className=\"text-sm text-gray-600\">{key}</span>\n                <span className=\"text-sm font-mono text-gray-800\">{String(value)}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Add Parameter Button */}\n      <button\n        onClick={() => {\n          const key = prompt('Parameter name:');\n          const value = prompt('Parameter value:');\n          if (key && value) {\n            const newParams = { ...localProperties.parameters, [key]: value };\n            handlePropertyChange('parameters', newParams);\n          }\n        }}\n        className=\"w-full px-4 py-2 text-sm text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50 transition-colors\"\n      >\n        Add Parameter\n      </button>\n    </div>\n  );\n};\n\nexport default PropertyPanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,MAAM,QACD,cAAc;AACrB,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,YAAY;EAAEC,YAAY;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM;IAAEC,UAAU;IAAEC;EAAW,CAAC,GAAGb,WAAW,CAAC,CAAC;EAChD,MAAM;IAAEc;EAAgB,CAAC,GAAGb,QAAQ,CAAC,CAAC;EACtC,MAAMc,KAAK,GAAGD,eAAe,CAAC,CAAC;EAC/B,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,EAAE;MAChBY,kBAAkB,CAACZ,YAAY,CAACa,IAAI,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM,IAAIZ,YAAY,EAAE;MACvBW,kBAAkB,CAACX,YAAY,CAACY,IAAI,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLD,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACZ,YAAY,EAAEC,YAAY,CAAC,CAAC;EAEhC,MAAMa,oBAAoB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3C,MAAMC,aAAa,GAAG;MAAE,GAAGN,eAAe;MAAE,CAACI,GAAG,GAAGC;IAAM,CAAC;IAC1DJ,kBAAkB,CAACK,aAAa,CAAC;IAEjC,IAAIjB,YAAY,EAAE;MAChBE,YAAY,CAACF,YAAY,CAACkB,EAAE,EAAE;QAAEL,IAAI,EAAEI;MAAc,CAAC,CAAC;IACxD,CAAC,MAAM,IAAIhB,YAAY,EAAE;MACvBE,YAAY,CAACF,YAAY,CAACiB,EAAE,EAAE;QAAEL,IAAI,EAAEI;MAAc,CAAC,CAAC;IACxD;EACF,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInB,YAAY,EAAE;MAChBK,UAAU,CAACL,YAAY,CAACkB,EAAE,CAAC;IAC7B,CAAC,MAAM,IAAIjB,YAAY,EAAE;MACvBK,UAAU,CAACL,YAAY,CAACiB,EAAE,CAAC;IAC7B;EACF,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIpB,YAAY,EAAE;MAChB,MAAMqB,OAAO,GAAG;QACd,GAAGrB,YAAY;QACfkB,EAAE,EAAE,GAAGlB,YAAY,CAACkB,EAAE,SAASI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC3CC,QAAQ,EAAE;UACRC,CAAC,EAAEzB,YAAY,CAACwB,QAAQ,CAACC,CAAC,GAAG,EAAE;UAC/BC,CAAC,EAAE1B,YAAY,CAACwB,QAAQ,CAACE,CAAC,GAAG;QAC/B;MACF,CAAC;MACD;MACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,OAAO,CAAC;IACzC;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI7B,YAAY,EAAE;MAChBc,oBAAoB,CAAC,QAAQ,EAAE,CAACH,eAAe,CAACmB,MAAM,CAAC;IACzD;EACF,CAAC;EAED,IAAI,CAAC9B,YAAY,IAAI,CAACC,YAAY,EAAE;IAClC,oBACEL,OAAA;MAAKmC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpC,OAAA;QAAKmC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CpC,OAAA,CAACd,QAAQ;UAACiD,SAAS,EAAC,SAAS;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE1B,KAAK,CAAC2B,MAAM,CAACC;UAAc;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9E5C,OAAA;UAAImC,SAAS,EAAC,uBAAuB;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE1B,KAAK,CAAC2B,MAAM,CAACM;UAAY,CAAE;UAAAT,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F,CAAC,eACN5C,OAAA;QAAKmC,SAAS,EAAC,kBAAkB;QAACE,KAAK,EAAE;UAAEC,KAAK,EAAE1B,KAAK,CAAC2B,MAAM,CAACO;QAAa,CAAE;QAAAV,QAAA,gBAC5EpC,OAAA,CAACd,QAAQ;UAACiD,SAAS,EAAC,wBAAwB;UAACE,KAAK,EAAE;YAAEC,KAAK,EAAE1B,KAAK,CAAC2B,MAAM,CAACQ;UAAU;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzF5C,OAAA;UAAAoC,QAAA,EAAG;QAAwC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5C,OAAA;IAAKmC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCpC,OAAA;MAAKmC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDpC,OAAA;QAAKmC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CpC,OAAA,CAACd,QAAQ;UAACiD,SAAS,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C5C,OAAA;UAAImC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACN5C,OAAA;QAAKmC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CpC,OAAA;UACEgD,OAAO,EAAEf,gBAAiB;UAC1BE,SAAS,EAAC,iDAAiD;UAC3Dc,KAAK,EAAElC,eAAe,CAACmB,MAAM,GAAG,MAAM,GAAG,MAAO;UAAAE,QAAA,EAE/CrB,eAAe,CAACmB,MAAM,gBACrBlC,OAAA,CAACL,MAAM;YAACwC,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5C5C,OAAA,CAACN,GAAG;YAACyC,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACzC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACT5C,OAAA;UACEgD,OAAO,EAAExB,eAAgB;UACzBW,SAAS,EAAC,iDAAiD;UAC3Dc,KAAK,EAAC,WAAW;UAAAb,QAAA,eAEjBpC,OAAA,CAACP,IAAI;YAAC0C,SAAS,EAAC;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACT5C,OAAA;UACEgD,OAAO,EAAEzB,YAAa;UACtBY,SAAS,EAAC,gDAAgD;UAC1Dc,KAAK,EAAC,QAAQ;UAAAb,QAAA,eAEdpC,OAAA,CAACR,MAAM;YAAC2C,SAAS,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5C,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBpC,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DpC,OAAA,CAACZ,IAAI;YAAC+C,SAAS,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5C,OAAA;UACEkD,IAAI,EAAC,MAAM;UACX9B,KAAK,EAAEL,eAAe,CAACoC,KAAK,IAAI,EAAG;UACnCC,QAAQ,EAAGC,CAAC,IAAKnC,oBAAoB,CAAC,OAAO,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;UAC/De,SAAS,EAAC,iIAAiI;UAC3IoB,WAAW,EAAC;QAAgB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5C,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5C,OAAA;UACEoB,KAAK,EAAEL,eAAe,CAACyC,WAAW,IAAI,EAAG;UACzCJ,QAAQ,EAAGC,CAAC,IAAKnC,oBAAoB,CAAC,aAAa,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;UACrEe,SAAS,EAAC,iIAAiI;UAC3IsB,IAAI,EAAC,GAAG;UACRF,WAAW,EAAC;QAAsB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5C,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DpC,OAAA,CAACb,OAAO;YAACgD,SAAS,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,SAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5C,OAAA;UAAKmC,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpC,OAAA;YACEgD,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDsB,SAAS,EAAC,gFAAgF;YAC1FE,KAAK,EAAE;cAAEqB,eAAe,EAAE3C,eAAe,CAACuB,KAAK,IAAI;YAAU,CAAE;YAAAF,QAAA,gBAE/DpC,OAAA;cACEmC,SAAS,EAAC,wCAAwC;cAClDE,KAAK,EAAE;gBAAEqB,eAAe,EAAE3C,eAAe,CAACuB,KAAK,IAAI;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eACF5C,OAAA;cAAMmC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACpCrB,eAAe,CAACuB,KAAK,IAAI;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EACR/B,eAAe,iBACdb,OAAA;YAAKmC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpC,OAAA;cACEmC,SAAS,EAAC,eAAe;cACzBa,OAAO,EAAEA,CAAA,KAAMlC,kBAAkB,CAAC,KAAK;YAAE;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACF5C,OAAA,CAACJ,YAAY;cACX0C,KAAK,EAAEvB,eAAe,CAACuB,KAAK,IAAI,SAAU;cAC1Cc,QAAQ,EAAGd,KAAK,IAAKpB,oBAAoB,CAAC,OAAO,EAAEoB,KAAK,CAACqB,GAAG;YAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxC,YAAY,iBACXJ,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,EAAC;QAEhE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5C,OAAA;UACEoB,KAAK,EAAEL,eAAe,CAAC6C,MAAM,IAAI,SAAU;UAC3CR,QAAQ,EAAGC,CAAC,IAAKnC,oBAAoB,CAAC,QAAQ,EAAEmC,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAE;UAChEe,SAAS,EAAC,iIAAiI;UAAAC,QAAA,gBAE3IpC,OAAA;YAAQoB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5C,OAAA;YAAQoB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxC5C,OAAA;YAAQoB,KAAK,EAAC,WAAW;YAAAgB,QAAA,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C5C,OAAA;YAAQoB,KAAK,EAAC,QAAQ;YAAAgB,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC5C,OAAA;YAAQoB,KAAK,EAAC,QAAQ;YAAAgB,QAAA,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC5C,OAAA;YAAQoB,KAAK,EAAC,SAAS;YAAAgB,QAAA,EAAC;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAxC,YAAY,iBACXJ,OAAA;QAAAoC,QAAA,gBACEpC,OAAA;UAAOmC,SAAS,EAAC,8CAA8C;UAAAC,QAAA,gBAC7DpC,OAAA,CAACX,IAAI;YAAC8C,SAAS,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACR5C,OAAA;UACEkD,IAAI,EAAC,QAAQ;UACbW,GAAG,EAAC,GAAG;UACPC,GAAG,EAAC,KAAK;UACT1C,KAAK,EAAEL,eAAe,CAACgD,QAAQ,IAAI,CAAE;UACrCX,QAAQ,EAAGC,CAAC,IAAKnC,oBAAoB,CAAC,UAAU,EAAE8C,QAAQ,CAACX,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAC,CAAE;UAC5Ee,SAAS,EAAC;QAAiI;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAvC,YAAY,iBACXL,OAAA,CAAAE,SAAA;QAAAkC,QAAA,gBACEpC,OAAA;UAAAoC,QAAA,eACEpC,OAAA;YAAOmC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAC3CrB,eAAe,CAACkD,MAAM,gBACrBjE,OAAA,CAACT,WAAW;cACV4C,SAAS,EAAC,sCAAsC;cAChDa,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAAC,QAAQ,EAAE,KAAK;YAAE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,gBAEF5C,OAAA,CAACV,UAAU;cACT6C,SAAS,EAAC,sCAAsC;cAChDa,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAAC,QAAQ,EAAE,IAAI;YAAE;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CACF,eACD5C,OAAA;cAAMmC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5C,OAAA;UAAAoC,QAAA,gBACEpC,OAAA;YAAOmC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR5C,OAAA;YACEkD,IAAI,EAAC,OAAO;YACZW,GAAG,EAAC,GAAG;YACPC,GAAG,EAAC,IAAI;YACR1C,KAAK,EAAEL,eAAe,CAACmD,WAAW,IAAI,CAAE;YACxCd,QAAQ,EAAGC,CAAC,IAAKnC,oBAAoB,CAAC,aAAa,EAAE8C,QAAQ,CAACX,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAC,CAAE;YAC/Ee,SAAS,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACF5C,OAAA;YAAKmC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACxCrB,eAAe,CAACmD,WAAW,IAAI,CAAC,EAAC,IACpC;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL7B,eAAe,CAACoD,UAAU,IAAIC,MAAM,CAACC,IAAI,CAACtD,eAAe,CAACoD,UAAU,CAAC,CAACG,MAAM,GAAG,CAAC,iBAC/EtE,OAAA;MAAAoC,QAAA,gBACEpC,OAAA;QAAImC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE5C,OAAA;QAAKmC,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBgC,MAAM,CAACG,OAAO,CAACxD,eAAe,CAACoD,UAAU,CAAC,CAACK,GAAG,CAAC,CAAC,CAACrD,GAAG,EAAEC,KAAK,CAAC,kBAC3DpB,OAAA;UAAemC,SAAS,EAAC,0DAA0D;UAAAC,QAAA,gBACjFpC,OAAA;YAAMmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEjB;UAAG;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD5C,OAAA;YAAMmC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAEqC,MAAM,CAACrD,KAAK;UAAC;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFhEzB,GAAG;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5C,OAAA;MACEgD,OAAO,EAAEA,CAAA,KAAM;QACb,MAAM7B,GAAG,GAAGuD,MAAM,CAAC,iBAAiB,CAAC;QACrC,MAAMtD,KAAK,GAAGsD,MAAM,CAAC,kBAAkB,CAAC;QACxC,IAAIvD,GAAG,IAAIC,KAAK,EAAE;UAChB,MAAMuD,SAAS,GAAG;YAAE,GAAG5D,eAAe,CAACoD,UAAU;YAAE,CAAChD,GAAG,GAAGC;UAAM,CAAC;UACjEF,oBAAoB,CAAC,YAAY,EAAEyD,SAAS,CAAC;QAC/C;MACF,CAAE;MACFxC,SAAS,EAAC,6GAA6G;MAAAC,QAAA,EACxH;IAED;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpC,EAAA,CA1RIL,aAAa;EAAA,QACkBN,WAAW,EAClBC,QAAQ;AAAA;AAAA8E,EAAA,GAFhCzE,aAAa;AA4RnB,eAAeA,aAAa;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}