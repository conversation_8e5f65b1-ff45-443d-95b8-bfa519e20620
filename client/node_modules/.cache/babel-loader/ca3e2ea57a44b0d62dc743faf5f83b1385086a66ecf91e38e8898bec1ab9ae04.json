{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nimport * as color from '../../helpers/color';\nimport { EditableInput } from '../common';\nexport var PhotoshopPicker = function PhotoshopPicker(_ref) {\n  var onChange = _ref.onChange,\n    rgb = _ref.rgb,\n    hsv = _ref.hsv,\n    hex = _ref.hex;\n  var styles = reactCSS({\n    'default': {\n      fields: {\n        paddingTop: '5px',\n        paddingBottom: '9px',\n        width: '80px',\n        position: 'relative'\n      },\n      divider: {\n        height: '5px'\n      },\n      RGBwrap: {\n        position: 'relative'\n      },\n      RGBinput: {\n        marginLeft: '40%',\n        width: '40%',\n        height: '18px',\n        border: '1px solid #888888',\n        boxShadow: 'inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC',\n        marginBottom: '5px',\n        fontSize: '13px',\n        paddingLeft: '3px',\n        marginRight: '10px'\n      },\n      RGBlabel: {\n        left: '0px',\n        top: '0px',\n        width: '34px',\n        textTransform: 'uppercase',\n        fontSize: '13px',\n        height: '18px',\n        lineHeight: '22px',\n        position: 'absolute'\n      },\n      HEXwrap: {\n        position: 'relative'\n      },\n      HEXinput: {\n        marginLeft: '20%',\n        width: '80%',\n        height: '18px',\n        border: '1px solid #888888',\n        boxShadow: 'inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC',\n        marginBottom: '6px',\n        fontSize: '13px',\n        paddingLeft: '3px'\n      },\n      HEXlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        width: '14px',\n        textTransform: 'uppercase',\n        fontSize: '13px',\n        height: '18px',\n        lineHeight: '22px'\n      },\n      fieldSymbols: {\n        position: 'absolute',\n        top: '5px',\n        right: '-7px',\n        fontSize: '13px'\n      },\n      symbol: {\n        height: '20px',\n        lineHeight: '22px',\n        paddingBottom: '7px'\n      }\n    }\n  });\n  var handleChange = function handleChange(data, e) {\n    if (data['#']) {\n      color.isValidHex(data['#']) && onChange({\n        hex: data['#'],\n        source: 'hex'\n      }, e);\n    } else if (data.r || data.g || data.b) {\n      onChange({\n        r: data.r || rgb.r,\n        g: data.g || rgb.g,\n        b: data.b || rgb.b,\n        source: 'rgb'\n      }, e);\n    } else if (data.h || data.s || data.v) {\n      onChange({\n        h: data.h || hsv.h,\n        s: data.s || hsv.s,\n        v: data.v || hsv.v,\n        source: 'hsv'\n      }, e);\n    }\n  };\n  return React.createElement('div', {\n    style: styles.fields\n  }, React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'h',\n    value: Math.round(hsv.h),\n    onChange: handleChange\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 's',\n    value: Math.round(hsv.s * 100),\n    onChange: handleChange\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'v',\n    value: Math.round(hsv.v * 100),\n    onChange: handleChange\n  }), React.createElement('div', {\n    style: styles.divider\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'r',\n    value: rgb.r,\n    onChange: handleChange\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'g',\n    value: rgb.g,\n    onChange: handleChange\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.RGBwrap,\n      input: styles.RGBinput,\n      label: styles.RGBlabel\n    },\n    label: 'b',\n    value: rgb.b,\n    onChange: handleChange\n  }), React.createElement('div', {\n    style: styles.divider\n  }), React.createElement(EditableInput, {\n    style: {\n      wrap: styles.HEXwrap,\n      input: styles.HEXinput,\n      label: styles.HEXlabel\n    },\n    label: '#',\n    value: hex.replace('#', ''),\n    onChange: handleChange\n  }), React.createElement('div', {\n    style: styles.fieldSymbols\n  }, React.createElement('div', {\n    style: styles.symbol\n  }, '\\xB0'), React.createElement('div', {\n    style: styles.symbol\n  }, '%'), React.createElement('div', {\n    style: styles.symbol\n  }, '%')));\n};\nexport default PhotoshopPicker;", "map": {"version": 3, "names": ["React", "reactCSS", "color", "EditableInput", "PhotoshopPicker", "_ref", "onChange", "rgb", "hsv", "hex", "styles", "fields", "paddingTop", "paddingBottom", "width", "position", "divider", "height", "RGBwrap", "RGBinput", "marginLeft", "border", "boxShadow", "marginBottom", "fontSize", "paddingLeft", "marginRight", "RGBlabel", "left", "top", "textTransform", "lineHeight", "HEXwrap", "HEXinput", "HEXlabel", "fieldSymbols", "right", "symbol", "handleChange", "data", "e", "isValidHex", "source", "r", "g", "b", "h", "s", "v", "createElement", "style", "wrap", "input", "label", "value", "Math", "round", "replace"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/photoshop/PhotoshopFields.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\nimport * as color from '../../helpers/color';\n\nimport { EditableInput } from '../common';\n\nexport var PhotoshopPicker = function PhotoshopPicker(_ref) {\n  var onChange = _ref.onChange,\n      rgb = _ref.rgb,\n      hsv = _ref.hsv,\n      hex = _ref.hex;\n\n  var styles = reactCSS({\n    'default': {\n      fields: {\n        paddingTop: '5px',\n        paddingBottom: '9px',\n        width: '80px',\n        position: 'relative'\n      },\n      divider: {\n        height: '5px'\n      },\n      RGBwrap: {\n        position: 'relative'\n      },\n      RGBinput: {\n        marginLeft: '40%',\n        width: '40%',\n        height: '18px',\n        border: '1px solid #888888',\n        boxShadow: 'inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC',\n        marginBottom: '5px',\n        fontSize: '13px',\n        paddingLeft: '3px',\n        marginRight: '10px'\n      },\n      RGBlabel: {\n        left: '0px',\n        top: '0px',\n        width: '34px',\n        textTransform: 'uppercase',\n        fontSize: '13px',\n        height: '18px',\n        lineHeight: '22px',\n        position: 'absolute'\n      },\n      HEXwrap: {\n        position: 'relative'\n      },\n      HEXinput: {\n        marginLeft: '20%',\n        width: '80%',\n        height: '18px',\n        border: '1px solid #888888',\n        boxShadow: 'inset 0 1px 1px rgba(0,0,0,.1), 0 1px 0 0 #ECECEC',\n        marginBottom: '6px',\n        fontSize: '13px',\n        paddingLeft: '3px'\n      },\n      HEXlabel: {\n        position: 'absolute',\n        top: '0px',\n        left: '0px',\n        width: '14px',\n        textTransform: 'uppercase',\n        fontSize: '13px',\n        height: '18px',\n        lineHeight: '22px'\n      },\n      fieldSymbols: {\n        position: 'absolute',\n        top: '5px',\n        right: '-7px',\n        fontSize: '13px'\n      },\n      symbol: {\n        height: '20px',\n        lineHeight: '22px',\n        paddingBottom: '7px'\n      }\n    }\n  });\n\n  var handleChange = function handleChange(data, e) {\n    if (data['#']) {\n      color.isValidHex(data['#']) && onChange({\n        hex: data['#'],\n        source: 'hex'\n      }, e);\n    } else if (data.r || data.g || data.b) {\n      onChange({\n        r: data.r || rgb.r,\n        g: data.g || rgb.g,\n        b: data.b || rgb.b,\n        source: 'rgb'\n      }, e);\n    } else if (data.h || data.s || data.v) {\n      onChange({\n        h: data.h || hsv.h,\n        s: data.s || hsv.s,\n        v: data.v || hsv.v,\n        source: 'hsv'\n      }, e);\n    }\n  };\n\n  return React.createElement(\n    'div',\n    { style: styles.fields },\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 'h',\n      value: Math.round(hsv.h),\n      onChange: handleChange\n    }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 's',\n      value: Math.round(hsv.s * 100),\n      onChange: handleChange\n    }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 'v',\n      value: Math.round(hsv.v * 100),\n      onChange: handleChange\n    }),\n    React.createElement('div', { style: styles.divider }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 'r',\n      value: rgb.r,\n      onChange: handleChange\n    }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 'g',\n      value: rgb.g,\n      onChange: handleChange\n    }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.RGBwrap, input: styles.RGBinput, label: styles.RGBlabel },\n      label: 'b',\n      value: rgb.b,\n      onChange: handleChange\n    }),\n    React.createElement('div', { style: styles.divider }),\n    React.createElement(EditableInput, {\n      style: { wrap: styles.HEXwrap, input: styles.HEXinput, label: styles.HEXlabel },\n      label: '#',\n      value: hex.replace('#', ''),\n      onChange: handleChange\n    }),\n    React.createElement(\n      'div',\n      { style: styles.fieldSymbols },\n      React.createElement(\n        'div',\n        { style: styles.symbol },\n        '\\xB0'\n      ),\n      React.createElement(\n        'div',\n        { style: styles.symbol },\n        '%'\n      ),\n      React.createElement(\n        'div',\n        { style: styles.symbol },\n        '%'\n      )\n    )\n  );\n};\n\nexport default PhotoshopPicker;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,SAASC,aAAa,QAAQ,WAAW;AAEzC,OAAO,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EAC1D,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,GAAG,GAAGH,IAAI,CAACG,GAAG;IACdC,GAAG,GAAGJ,IAAI,CAACI,GAAG;EAElB,IAAIC,MAAM,GAAGT,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTU,MAAM,EAAE;QACNC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,KAAK;QACpBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;QACPC,MAAM,EAAE;MACV,CAAC;MACDC,OAAO,EAAE;QACPH,QAAQ,EAAE;MACZ,CAAC;MACDI,QAAQ,EAAE;QACRC,UAAU,EAAE,KAAK;QACjBN,KAAK,EAAE,KAAK;QACZG,MAAM,EAAE,MAAM;QACdI,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,mDAAmD;QAC9DC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE,KAAK;QAClBC,WAAW,EAAE;MACf,CAAC;MACDC,QAAQ,EAAE;QACRC,IAAI,EAAE,KAAK;QACXC,GAAG,EAAE,KAAK;QACVf,KAAK,EAAE,MAAM;QACbgB,aAAa,EAAE,WAAW;QAC1BN,QAAQ,EAAE,MAAM;QAChBP,MAAM,EAAE,MAAM;QACdc,UAAU,EAAE,MAAM;QAClBhB,QAAQ,EAAE;MACZ,CAAC;MACDiB,OAAO,EAAE;QACPjB,QAAQ,EAAE;MACZ,CAAC;MACDkB,QAAQ,EAAE;QACRb,UAAU,EAAE,KAAK;QACjBN,KAAK,EAAE,KAAK;QACZG,MAAM,EAAE,MAAM;QACdI,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE,mDAAmD;QAC9DC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE;MACf,CAAC;MACDS,QAAQ,EAAE;QACRnB,QAAQ,EAAE,UAAU;QACpBc,GAAG,EAAE,KAAK;QACVD,IAAI,EAAE,KAAK;QACXd,KAAK,EAAE,MAAM;QACbgB,aAAa,EAAE,WAAW;QAC1BN,QAAQ,EAAE,MAAM;QAChBP,MAAM,EAAE,MAAM;QACdc,UAAU,EAAE;MACd,CAAC;MACDI,YAAY,EAAE;QACZpB,QAAQ,EAAE,UAAU;QACpBc,GAAG,EAAE,KAAK;QACVO,KAAK,EAAE,MAAM;QACbZ,QAAQ,EAAE;MACZ,CAAC;MACDa,MAAM,EAAE;QACNpB,MAAM,EAAE,MAAM;QACdc,UAAU,EAAE,MAAM;QAClBlB,aAAa,EAAE;MACjB;IACF;EACF,CAAC,CAAC;EAEF,IAAIyB,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,CAAC,EAAE;IAChD,IAAID,IAAI,CAAC,GAAG,CAAC,EAAE;MACbrC,KAAK,CAACuC,UAAU,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,IAAIjC,QAAQ,CAAC;QACtCG,GAAG,EAAE8B,IAAI,CAAC,GAAG,CAAC;QACdG,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP,CAAC,MAAM,IAAID,IAAI,CAACI,CAAC,IAAIJ,IAAI,CAACK,CAAC,IAAIL,IAAI,CAACM,CAAC,EAAE;MACrCvC,QAAQ,CAAC;QACPqC,CAAC,EAAEJ,IAAI,CAACI,CAAC,IAAIpC,GAAG,CAACoC,CAAC;QAClBC,CAAC,EAAEL,IAAI,CAACK,CAAC,IAAIrC,GAAG,CAACqC,CAAC;QAClBC,CAAC,EAAEN,IAAI,CAACM,CAAC,IAAItC,GAAG,CAACsC,CAAC;QAClBH,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP,CAAC,MAAM,IAAID,IAAI,CAACO,CAAC,IAAIP,IAAI,CAACQ,CAAC,IAAIR,IAAI,CAACS,CAAC,EAAE;MACrC1C,QAAQ,CAAC;QACPwC,CAAC,EAAEP,IAAI,CAACO,CAAC,IAAItC,GAAG,CAACsC,CAAC;QAClBC,CAAC,EAAER,IAAI,CAACQ,CAAC,IAAIvC,GAAG,CAACuC,CAAC;QAClBC,CAAC,EAAET,IAAI,CAACS,CAAC,IAAIxC,GAAG,CAACwC,CAAC;QAClBN,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP;EACF,CAAC;EAED,OAAOxC,KAAK,CAACiD,aAAa,CACxB,KAAK,EACL;IAAEC,KAAK,EAAExC,MAAM,CAACC;EAAO,CAAC,EACxBX,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAAChD,GAAG,CAACsC,CAAC,CAAC;IACxBxC,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAAChD,GAAG,CAACuC,CAAC,GAAG,GAAG,CAAC;IAC9BzC,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAEC,IAAI,CAACC,KAAK,CAAChD,GAAG,CAACwC,CAAC,GAAG,GAAG,CAAC;IAC9B1C,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAExC,MAAM,CAACM;EAAQ,CAAC,CAAC,EACrDhB,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE/C,GAAG,CAACoC,CAAC;IACZrC,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE/C,GAAG,CAACqC,CAAC;IACZtC,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACQ,OAAO;MAAEkC,KAAK,EAAE1C,MAAM,CAACS,QAAQ;MAAEkC,KAAK,EAAE3C,MAAM,CAACiB;IAAS,CAAC;IAC/E0B,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE/C,GAAG,CAACsC,CAAC;IACZvC,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAExC,MAAM,CAACM;EAAQ,CAAC,CAAC,EACrDhB,KAAK,CAACiD,aAAa,CAAC9C,aAAa,EAAE;IACjC+C,KAAK,EAAE;MAAEC,IAAI,EAAEzC,MAAM,CAACsB,OAAO;MAAEoB,KAAK,EAAE1C,MAAM,CAACuB,QAAQ;MAAEoB,KAAK,EAAE3C,MAAM,CAACwB;IAAS,CAAC;IAC/EmB,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE7C,GAAG,CAACgD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAC3BnD,QAAQ,EAAEgC;EACZ,CAAC,CAAC,EACFtC,KAAK,CAACiD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAExC,MAAM,CAACyB;EAAa,CAAC,EAC9BnC,KAAK,CAACiD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAExC,MAAM,CAAC2B;EAAO,CAAC,EACxB,MACF,CAAC,EACDrC,KAAK,CAACiD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAExC,MAAM,CAAC2B;EAAO,CAAC,EACxB,GACF,CAAC,EACDrC,KAAK,CAACiD,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAExC,MAAM,CAAC2B;EAAO,CAAC,EACxB,GACF,CACF,CACF,CAAC;AACH,CAAC;AAED,eAAejC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}