{"ast": null, "code": "import React from 'react';\nimport reactCSS from 'reactcss';\nimport PropTypes from 'prop-types';\nexport var GooglePointerCircle = function GooglePointerCircle(props) {\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        width: '20px',\n        height: '20px',\n        borderRadius: '22px',\n        border: '2px #fff solid',\n        transform: 'translate(-12px, -13px)',\n        background: 'hsl(' + Math.round(props.hsl.h) + ', ' + Math.round(props.hsl.s * 100) + '%, ' + Math.round(props.hsl.l * 100) + '%)'\n      }\n    }\n  });\n  return React.createElement('div', {\n    style: styles.picker\n  });\n};\nGooglePointerCircle.propTypes = {\n  hsl: PropTypes.shape({\n    h: PropTypes.number,\n    s: PropTypes.number,\n    l: PropTypes.number,\n    a: PropTypes.number\n  })\n};\nGooglePointerCircle.defaultProps = {\n  hsl: {\n    a: 1,\n    h: 249.94,\n    l: 0.2,\n    s: 0.50\n  }\n};\nexport default GooglePointerCircle;", "map": {"version": 3, "names": ["React", "reactCSS", "PropTypes", "GooglePointerCircle", "props", "styles", "picker", "width", "height", "borderRadius", "border", "transform", "background", "Math", "round", "hsl", "h", "s", "l", "createElement", "style", "propTypes", "shape", "number", "a", "defaultProps"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/google/GooglePointerCircle.js"], "sourcesContent": ["import React from 'react';\nimport reactCSS from 'reactcss';\nimport PropTypes from 'prop-types';\n\nexport var GooglePointerCircle = function GooglePointerCircle(props) {\n  var styles = reactCSS({\n    'default': {\n      picker: {\n        width: '20px',\n        height: '20px',\n        borderRadius: '22px',\n        border: '2px #fff solid',\n        transform: 'translate(-12px, -13px)',\n        background: 'hsl(' + Math.round(props.hsl.h) + ', ' + Math.round(props.hsl.s * 100) + '%, ' + Math.round(props.hsl.l * 100) + '%)'\n      }\n    }\n  });\n\n  return React.createElement('div', { style: styles.picker });\n};\n\nGooglePointerCircle.propTypes = {\n  hsl: PropTypes.shape({\n    h: PropTypes.number,\n    s: PropTypes.number,\n    l: PropTypes.number,\n    a: PropTypes.number\n  })\n};\n\nGooglePointerCircle.defaultProps = {\n  hsl: { a: 1, h: 249.94, l: 0.2, s: 0.50 }\n};\n\nexport default GooglePointerCircle;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,SAAS,MAAM,YAAY;AAElC,OAAO,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAE;EACnE,IAAIC,MAAM,GAAGJ,QAAQ,CAAC;IACpB,SAAS,EAAE;MACTK,MAAM,EAAE;QACNC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,gBAAgB;QACxBC,SAAS,EAAE,yBAAyB;QACpCC,UAAU,EAAE,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACV,KAAK,CAACW,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,GAAGH,IAAI,CAACC,KAAK,CAACV,KAAK,CAACW,GAAG,CAACE,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAGJ,IAAI,CAACC,KAAK,CAACV,KAAK,CAACW,GAAG,CAACG,CAAC,GAAG,GAAG,CAAC,GAAG;MAChI;IACF;EACF,CAAC,CAAC;EAEF,OAAOlB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEf,MAAM,CAACC;EAAO,CAAC,CAAC;AAC7D,CAAC;AAEDH,mBAAmB,CAACkB,SAAS,GAAG;EAC9BN,GAAG,EAAEb,SAAS,CAACoB,KAAK,CAAC;IACnBN,CAAC,EAAEd,SAAS,CAACqB,MAAM;IACnBN,CAAC,EAAEf,SAAS,CAACqB,MAAM;IACnBL,CAAC,EAAEhB,SAAS,CAACqB,MAAM;IACnBC,CAAC,EAAEtB,SAAS,CAACqB;EACf,CAAC;AACH,CAAC;AAEDpB,mBAAmB,CAACsB,YAAY,GAAG;EACjCV,GAAG,EAAE;IAAES,CAAC,EAAE,CAAC;IAAER,CAAC,EAAE,MAAM;IAAEE,CAAC,EAAE,GAAG;IAAED,CAAC,EAAE;EAAK;AAC1C,CAAC;AAED,eAAed,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}