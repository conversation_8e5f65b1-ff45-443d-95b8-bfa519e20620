{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst CornerDownLeft = createLucideIcon(\"CornerDownLeft\", [[\"polyline\", {\n  points: \"9 10 4 15 9 20\",\n  key: \"r3jprv\"\n}], [\"path\", {\n  d: \"M20 4v7a4 4 0 0 1-4 4H4\",\n  key: \"6o5b7l\"\n}]]);\nexport { CornerDownLeft as default };", "map": {"version": 3, "names": ["CornerDownLeft", "createLucideIcon", "points", "key", "d"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/lucide-react/src/icons/corner-down-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CornerDownLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI5IDEwIDQgMTUgOSAyMCIgLz4KICA8cGF0aCBkPSJNMjAgNHY3YTQgNCAwIDAgMS00IDRINCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-down-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerDownLeft = createLucideIcon('CornerDownLeft', [\n  ['polyline', { points: '9 10 4 15 9 20', key: 'r3jprv' }],\n  ['path', { d: 'M20 4v7a4 4 0 0 1-4 4H4', key: '6o5b7l' }],\n]);\n\nexport default CornerDownLeft;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}