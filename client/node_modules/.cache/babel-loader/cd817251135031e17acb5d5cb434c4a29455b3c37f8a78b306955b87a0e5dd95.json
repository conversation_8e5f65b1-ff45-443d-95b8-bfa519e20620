{"ast": null, "code": "import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\nimport { ColorWrap, Raised } from '../common';\nimport CompactColor from './CompactColor';\nimport CompactFields from './CompactFields';\nexport var Compact = function Compact(_ref) {\n  var onChange = _ref.onChange,\n    onSwatchHover = _ref.onSwatchHover,\n    colors = _ref.colors,\n    hex = _ref.hex,\n    rgb = _ref.rgb,\n    _ref$styles = _ref.styles,\n    passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n    _ref$className = _ref.className,\n    className = _ref$className === undefined ? '' : _ref$className;\n  var styles = reactCSS(merge({\n    'default': {\n      Compact: {\n        background: '#f6f6f6',\n        radius: '4px'\n      },\n      compact: {\n        paddingTop: '5px',\n        paddingLeft: '5px',\n        boxSizing: 'initial',\n        width: '240px'\n      },\n      clear: {\n        clear: 'both'\n      }\n    }\n  }, passedStyles));\n  var handleChange = function handleChange(data, e) {\n    if (data.hex) {\n      color.isValidHex(data.hex) && onChange({\n        hex: data.hex,\n        source: 'hex'\n      }, e);\n    } else {\n      onChange(data, e);\n    }\n  };\n  return React.createElement(Raised, {\n    style: styles.Compact,\n    styles: passedStyles\n  }, React.createElement('div', {\n    style: styles.compact,\n    className: 'compact-picker ' + className\n  }, React.createElement('div', null, map(colors, function (c) {\n    return React.createElement(CompactColor, {\n      key: c,\n      color: c,\n      active: c.toLowerCase() === hex,\n      onClick: handleChange,\n      onSwatchHover: onSwatchHover\n    });\n  }), React.createElement('div', {\n    style: styles.clear\n  })), React.createElement(CompactFields, {\n    hex: hex,\n    rgb: rgb,\n    onChange: handleChange\n  })));\n};\nCompact.propTypes = {\n  colors: PropTypes.arrayOf(PropTypes.string),\n  styles: PropTypes.object\n};\nCompact.defaultProps = {\n  colors: ['#4D4D4D', '#999999', '#FFFFFF', '#F44E3B', '#FE9200', '#FCDC00', '#DBDF00', '#A4DD00', '#68CCCA', '#73D8FF', '#AEA1FF', '#FDA1FF', '#333333', '#808080', '#cccccc', '#D33115', '#E27300', '#FCC400', '#B0BC00', '#68BC00', '#16A5A5', '#009CE0', '#7B64FF', '#FA28FF', '#000000', '#666666', '#B3B3B3', '#9F0500', '#C45100', '#FB9E00', '#808900', '#194D33', '#0C797D', '#0062B1', '#653294', '#AB149E'],\n  styles: {}\n};\nexport default ColorWrap(Compact);", "map": {"version": 3, "names": ["React", "PropTypes", "reactCSS", "map", "merge", "color", "ColorWrap", "Raised", "CompactColor", "Compact<PERSON><PERSON><PERSON>", "Compact", "_ref", "onChange", "onSwatchHover", "colors", "hex", "rgb", "_ref$styles", "styles", "passedStyles", "undefined", "_ref$className", "className", "background", "radius", "compact", "paddingTop", "paddingLeft", "boxSizing", "width", "clear", "handleChange", "data", "e", "isValidHex", "source", "createElement", "style", "c", "key", "active", "toLowerCase", "onClick", "propTypes", "arrayOf", "string", "object", "defaultProps"], "sources": ["/home/<USER>/vlsi-workflow/client/node_modules/react-color/es/components/compact/Compact.js"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\nimport reactCSS from 'reactcss';\nimport map from 'lodash-es/map';\nimport merge from 'lodash-es/merge';\nimport * as color from '../../helpers/color';\n\nimport { ColorWrap, Raised } from '../common';\nimport CompactColor from './CompactColor';\nimport CompactFields from './CompactFields';\n\nexport var Compact = function Compact(_ref) {\n  var onChange = _ref.onChange,\n      onSwatchHover = _ref.onSwatchHover,\n      colors = _ref.colors,\n      hex = _ref.hex,\n      rgb = _ref.rgb,\n      _ref$styles = _ref.styles,\n      passedStyles = _ref$styles === undefined ? {} : _ref$styles,\n      _ref$className = _ref.className,\n      className = _ref$className === undefined ? '' : _ref$className;\n\n  var styles = reactCSS(merge({\n    'default': {\n      Compact: {\n        background: '#f6f6f6',\n        radius: '4px'\n      },\n      compact: {\n        paddingTop: '5px',\n        paddingLeft: '5px',\n        boxSizing: 'initial',\n        width: '240px'\n      },\n      clear: {\n        clear: 'both'\n      }\n    }\n  }, passedStyles));\n\n  var handleChange = function handleChange(data, e) {\n    if (data.hex) {\n      color.isValidHex(data.hex) && onChange({\n        hex: data.hex,\n        source: 'hex'\n      }, e);\n    } else {\n      onChange(data, e);\n    }\n  };\n\n  return React.createElement(\n    Raised,\n    { style: styles.Compact, styles: passedStyles },\n    React.createElement(\n      'div',\n      { style: styles.compact, className: 'compact-picker ' + className },\n      React.createElement(\n        'div',\n        null,\n        map(colors, function (c) {\n          return React.createElement(CompactColor, {\n            key: c,\n            color: c,\n            active: c.toLowerCase() === hex,\n            onClick: handleChange,\n            onSwatchHover: onSwatchHover\n          });\n        }),\n        React.createElement('div', { style: styles.clear })\n      ),\n      React.createElement(CompactFields, { hex: hex, rgb: rgb, onChange: handleChange })\n    )\n  );\n};\n\nCompact.propTypes = {\n  colors: PropTypes.arrayOf(PropTypes.string),\n  styles: PropTypes.object\n};\n\nCompact.defaultProps = {\n  colors: ['#4D4D4D', '#999999', '#FFFFFF', '#F44E3B', '#FE9200', '#FCDC00', '#DBDF00', '#A4DD00', '#68CCCA', '#73D8FF', '#AEA1FF', '#FDA1FF', '#333333', '#808080', '#cccccc', '#D33115', '#E27300', '#FCC400', '#B0BC00', '#68BC00', '#16A5A5', '#009CE0', '#7B64FF', '#FA28FF', '#000000', '#666666', '#B3B3B3', '#9F0500', '#C45100', '#FB9E00', '#808900', '#194D33', '#0C797D', '#0062B1', '#653294', '#AB149E'],\n  styles: {}\n};\n\nexport default ColorWrap(Compact);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,GAAG,MAAM,eAAe;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAE5C,SAASC,SAAS,EAAEC,MAAM,QAAQ,WAAW;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EAC1C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,aAAa,GAAGF,IAAI,CAACE,aAAa;IAClCC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,GAAG,GAAGJ,IAAI,CAACI,GAAG;IACdC,GAAG,GAAGL,IAAI,CAACK,GAAG;IACdC,WAAW,GAAGN,IAAI,CAACO,MAAM;IACzBC,YAAY,GAAGF,WAAW,KAAKG,SAAS,GAAG,CAAC,CAAC,GAAGH,WAAW;IAC3DI,cAAc,GAAGV,IAAI,CAACW,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAKD,SAAS,GAAG,EAAE,GAAGC,cAAc;EAElE,IAAIH,MAAM,GAAGhB,QAAQ,CAACE,KAAK,CAAC;IAC1B,SAAS,EAAE;MACTM,OAAO,EAAE;QACPa,UAAU,EAAE,SAAS;QACrBC,MAAM,EAAE;MACV,CAAC;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,KAAK;QACjBC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE;MACT;IACF;EACF,CAAC,EAAEX,YAAY,CAAC,CAAC;EAEjB,IAAIY,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,CAAC,EAAE;IAChD,IAAID,IAAI,CAACjB,GAAG,EAAE;MACZV,KAAK,CAAC6B,UAAU,CAACF,IAAI,CAACjB,GAAG,CAAC,IAAIH,QAAQ,CAAC;QACrCG,GAAG,EAAEiB,IAAI,CAACjB,GAAG;QACboB,MAAM,EAAE;MACV,CAAC,EAAEF,CAAC,CAAC;IACP,CAAC,MAAM;MACLrB,QAAQ,CAACoB,IAAI,EAAEC,CAAC,CAAC;IACnB;EACF,CAAC;EAED,OAAOjC,KAAK,CAACoC,aAAa,CACxB7B,MAAM,EACN;IAAE8B,KAAK,EAAEnB,MAAM,CAACR,OAAO;IAAEQ,MAAM,EAAEC;EAAa,CAAC,EAC/CnB,KAAK,CAACoC,aAAa,CACjB,KAAK,EACL;IAAEC,KAAK,EAAEnB,MAAM,CAACO,OAAO;IAAEH,SAAS,EAAE,iBAAiB,GAAGA;EAAU,CAAC,EACnEtB,KAAK,CAACoC,aAAa,CACjB,KAAK,EACL,IAAI,EACJjC,GAAG,CAACW,MAAM,EAAE,UAAUwB,CAAC,EAAE;IACvB,OAAOtC,KAAK,CAACoC,aAAa,CAAC5B,YAAY,EAAE;MACvC+B,GAAG,EAAED,CAAC;MACNjC,KAAK,EAAEiC,CAAC;MACRE,MAAM,EAAEF,CAAC,CAACG,WAAW,CAAC,CAAC,KAAK1B,GAAG;MAC/B2B,OAAO,EAAEX,YAAY;MACrBlB,aAAa,EAAEA;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC,EACFb,KAAK,CAACoC,aAAa,CAAC,KAAK,EAAE;IAAEC,KAAK,EAAEnB,MAAM,CAACY;EAAM,CAAC,CACpD,CAAC,EACD9B,KAAK,CAACoC,aAAa,CAAC3B,aAAa,EAAE;IAAEM,GAAG,EAAEA,GAAG;IAAEC,GAAG,EAAEA,GAAG;IAAEJ,QAAQ,EAAEmB;EAAa,CAAC,CACnF,CACF,CAAC;AACH,CAAC;AAEDrB,OAAO,CAACiC,SAAS,GAAG;EAClB7B,MAAM,EAAEb,SAAS,CAAC2C,OAAO,CAAC3C,SAAS,CAAC4C,MAAM,CAAC;EAC3C3B,MAAM,EAAEjB,SAAS,CAAC6C;AACpB,CAAC;AAEDpC,OAAO,CAACqC,YAAY,GAAG;EACrBjC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACpZI,MAAM,EAAE,CAAC;AACX,CAAC;AAED,eAAeZ,SAAS,CAACI,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}