import e from"postcss-value-parser";const r=r=>{const n=Object.assign({preserve:!1},r);return{postcssPlugin:"postcss-text-decoration-shorthand",prepare(){const r=new Map;return{OnceExit:()=>{r.clear()},Declaration:i=>{if("text-decoration"!==i.prop.toLowerCase())return;const s=i.parent.index(i);if(i.parent.nodes.some((e=>"decl"===e.type&&"text-decoration"===e.prop.toLowerCase()&&r.get(i.value)===e.value&&i.parent.index(e)!==s)))return;const u=e(i.value).nodes.filter((e=>"space"!==e.type&&"comment"!==e.type));if(u.length>4)return;if(u.find((e=>"var"===e.value.toLowerCase()&&"function"===e.type)))return;if(u.find((e=>"word"===e.type&&o.includes(e.value))))return;const d={line:null,style:null,color:null,thickness:null};for(let e=0;e<u.length;e++){const r=u[e];"word"===r.type&&t.includes(r.value.toLowerCase())?d.line=r:"word"===r.type&&a.includes(r.value.toLowerCase())?d.style=r:l(r)?d.color=r:"word"!==r.type||"none"!==r.value.toLowerCase()?d.thickness=r:(d.color||(d.color=r),d.line||(d.line=r))}d.line||(d.line={type:"word",value:"none"}),d.style||(d.style={type:"word",value:"solid"}),d.color||(d.color={type:"word",value:"currentColor"});try{const r=e.unit(d.thickness.value);r&&"%"===r.unit&&(d.thickness={type:"function",value:"calc",nodes:[{type:"word",value:"0.01em"},{type:"space",value:" "},{type:"word",value:"*"},{type:"space",value:" "},{type:"word",value:r.number}]})}catch(e){}const c=e.stringify(d.line);if(i.value.toLowerCase()===c)return;const p=e.stringify([d.line,{type:"space",value:" "},d.style,{type:"space",value:" "},d.color]);i.cloneBefore({prop:"text-decoration",value:c}),(d.thickness||3!==u.length)&&i.cloneBefore({prop:"text-decoration",value:p}),d.thickness&&i.cloneBefore({prop:"text-decoration-thickness",value:e.stringify([d.thickness])}),r.set(i.value,c),r.set(p,c),n.preserve||i.remove()}}}}};function l(e){return!("word"!==e.type||!e.value.startsWith("#"))||(!("word"!==e.type||!i.includes(e.value.toLowerCase()))||!("function"!==e.type||!n.includes(e.value.toLowerCase())))}r.postcss=!0;const o=["unset","inherit","initial","revert","revert-layer"],t=["underline","overline","line-through","blink","spelling-error","grammar-error"],a=["solid","double","dotted","dashed","wavy"],n=["rgb","rgba","hsl","hsla","hwb","lch","lab","color","oklch","oklab"],i=["currentcolor","aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"];export{r as default};
