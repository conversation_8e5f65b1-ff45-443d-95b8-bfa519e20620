{"version": 3, "file": "prefer-optional-chain.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-optional-chain.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,qCAA6C;AAC7C,+CAAiC;AAEjC,8CAAgC;AAYhC;;;;;;;;;;;;;;EAcE;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,yHAAyH;YAC3H,WAAW,EAAE,QAAQ;SACtB;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,mBAAmB,EACjB,6FAA6F;YAC/F,oBAAoB,EAAE,8BAA8B;SACrD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE7D,OAAO;YACL,oEAAoE,CAClE,IAAgC;gBAEhC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,+BAA+B,GACnC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAClD,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;gBACpC,IACE,CAAC,+BAA+B;oBAChC,CAAC,UAAU;oBACX,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACnD,UAAU,CAAC,QAAQ,EACnB;oBACA,OAAO;iBACR;gBAED,SAAS,yBAAyB;oBAChC,MAAM,aAAa,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAErE,MAAM,UAAU,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACtE,MAAM,QAAQ,GAAG,IAAA,4BAAkB,EAAC,aAAa,CAAC;wBAChD,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI;wBAClC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;oBAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAC/C,UAAU,CAAC,IAAI,EACf,QAAQ,CACT,CAAC;oBAEF,OAAO,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;gBAC/D,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,sBAAsB;oBACjC,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,sBAAsB;4BACjC,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE;gCAC/B,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gCAClD,wEAAwE;gCACxE,MAAM,oBAAoB,GAAG,yBAAyB,EAAE;oCACtD,CAAC,CAAC,IAAI,YAAY,GAAG;oCACrB,CAAC,CAAC,YAAY,CAAC;gCACjB,MAAM,wBAAwB,GAAG,UAAU,CAAC,OAAO,CACjD,UAAU,CAAC,QAAQ,CACpB,CAAC;gCACF,MAAM,oBAAoB,GAAG,UAAU,CAAC,QAAQ;oCAC9C,CAAC,CAAC,IAAI,wBAAwB,GAAG;oCACjC,CAAC,CAAC,wBAAwB,CAAC;gCAC7B,OAAO,KAAK,CAAC,gBAAgB,CAC3B,UAAU,CAAC,KAAK,EAChB,GAAG,oBAAoB,KAAK,oBAAoB,EAAE,CACnD,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YACD,CAAC;gBACC,+EAA+E;gBAC/E,qFAAqF;gBACrF,uGAAuG;gBACvG,iFAAiF;aAClF,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CACV,gCAGyB;gBAEzB,gCAAgC;gBAChC,MAAM,iBAAiB,GAAG,CACxB,gCAAgC,CAAC,MAAO,CAAC,IAAI;oBAC7C,sBAAc,CAAC,eAAe;oBAC5B,CAAC,CAAC,gCAAgC,CAAC,MAAM,CAAC,MAAM;oBAChD,CAAC,CAAC,gCAAgC,CAAC,MAAM,CAC3C,CAAC,MAAoC,CAAC;gBAExC,IACE,iBAAiB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBAC9D,iBAAiB,CAAC,IAAI,CAAC,QAAQ,KAAK,gCAAgC,EACpE;oBACA,yEAAyE;oBACzE,OAAO;iBACR;gBAED,6EAA6E;gBAC7E,IAAI,QAAQ,GAA+B,iBAAiB,CAAC;gBAC7D,IAAI,OAAO,GAAkB,iBAAiB,CAAC;gBAC/C,IAAI,gBAAgB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBACjE,IAAI,qBAAqB,GAAG,gBAAgB,CAAC;gBAC7C,IAAI,eAAe,GAAG,CAAC,CAAC;gBACxB,OAAO,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;oBACxD,IACE,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBACrD,CAAC,kBAAkB,CACjB,OAAO,CAAC,KAAK,CAAC,QAAQ;wBACtB,6EAA6E;wBAC7E,eAAe,KAAK,CAAC,CACtB,EACD;wBACA,MAAM;qBACP;oBACD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;wBAChD,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ;wBACjC,gBAAgB;qBACjB,CAAC,CAAC;oBACH,IAAI,WAAW,EAAE;wBACf,MAAM;qBACP;oBAED,IAAI,uCAAuC,CAAC;oBAC5C,CAAC;wBACC,uCAAuC;wBACvC,eAAe;wBACf,gBAAgB;wBAChB,qBAAqB;wBACrB,QAAQ;wBACR,OAAO;qBACR,GAAG,0BAA0B,CAC5B,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,EACR,OAAO,CACR,CAAC,CAAC;oBACH,IAAI,uCAAuC,EAAE;wBAC3C,OAAO;qBACR;iBACF;gBAED,mBAAmB,CAAC;oBAClB,eAAe;oBACf,QAAQ;oBACR,qBAAqB;oBACrB,UAAU;oBACV,OAAO;oBACP,uBAAuB,EAAE,KAAK;iBAC/B,CAAC,CAAC;YACL,CAAC;YACD,CAAC;gBACC,+CAA+C;gBAC/C,qDAAqD;gBACrD,uEAAuE;gBACvE,iDAAiD;gBACjD,qEAAqE;gBACrE,oEAAoE;aACrE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CACV,gCAIyB;;gBAEzB,gCAAgC;gBAChC,MAAM,iBAAiB,GAAG,CACxB,CAAA,MAAA,gCAAgC,CAAC,MAAM,0CAAE,IAAI;oBAC7C,sBAAc,CAAC,eAAe;oBAC5B,CAAC,CAAC,gCAAgC,CAAC,MAAM,CAAC,MAAM;oBAChD,CAAC,CAAC,gCAAgC,CAAC,MAAM,CACd,CAAC;gBAEhC,IAAI,iBAAiB,CAAC,IAAI,KAAK,gCAAgC,EAAE;oBAC/D,yEAAyE;oBACzE,OAAO;iBACR;gBACD,IAAI,CAAC,kBAAkB,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE;oBAC/D,OAAO;iBACR;gBAED,6EAA6E;gBAC7E,IAAI,QAAQ,GAA+B,iBAAiB,CAAC;gBAC7D,IAAI,OAAO,GAAkB,iBAAiB,CAAC;gBAC/C,IAAI,gBAAgB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBACjE,IAAI,qBAAqB,GAAG,gBAAgB,CAAC;gBAC7C,IAAI,eAAe,GAAG,CAAC,CAAC;gBACxB,OAAO,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;oBACxD,IACE,CAAC,kBAAkB,CACjB,OAAO,CAAC,KAAK;oBACb,4DAA4D;oBAC5D,eAAe,KAAK,CAAC,CACtB,EACD;wBACA,MAAM;qBACP;oBACD,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,cAAc,CAAC;wBAChD,SAAS,EAAE,OAAO,CAAC,KAAK;wBACxB,gBAAgB;qBACjB,CAAC,CAAC;oBACH,IAAI,WAAW,EAAE;wBACf,MAAM;qBACP;oBAED,IAAI,uCAAuC,CAAC;oBAC5C,CAAC;wBACC,uCAAuC;wBACvC,eAAe;wBACf,gBAAgB;wBAChB,qBAAqB;wBACrB,QAAQ;wBACR,OAAO;qBACR,GAAG,0BAA0B,CAC5B,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,qBAAqB,EACrB,QAAQ,EACR,OAAO,CACR,CAAC,CAAC;oBACH,IAAI,uCAAuC,EAAE;wBAC3C,OAAO;qBACR;iBACF;gBAED,mBAAmB,CAAC;oBAClB,eAAe;oBACf,QAAQ;oBACR,qBAAqB;oBACrB,UAAU;oBACV,OAAO;oBACP,uBAAuB,EAAE,IAAI;iBAC9B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAaF,SAAS,cAAc,CAAC,EACtB,gBAAgB,EAChB,SAAS,GACa;YACtB,IAAI,WAAW,GAAG,KAAK,CAAC;YAExB,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YACrC,qEAAqE;YACrE,MAAM,UAAU,GAAG,IAAI,MAAM,CAC3B,IAAI;YACF,0BAA0B;YAC1B,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CACxD,gBAAgB,CACjB,CAAC;YACF,IACE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC3B,iDAAiD;gBACjD,gBAAgB,KAAK,SAAS,EAC9B;gBACA,WAAW,GAAG,IAAI,CAAC;aACpB;YACD,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC;QAChE,CAAC;QAED,SAAS,OAAO,CAAC,IAAsB;YACrC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;gBACjD,OAAO,OAAO;gBACZ,+CAA+C;gBAC/C,IAAI,CAAC,IAAwB,CAC9B,CAAC;aACH;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAC/C,MAAM,UAAU,GAAG,OAAO;gBACxB,+CAA+C;gBAC/C,IAAI,CAAC,MAA0B,CAChC,CAAC;gBAEF,wGAAwG;gBACxG,2CAA2C;gBAC3C,uDAAuD;gBACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CACvC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,EAC7B,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,CACtE,CAAC;gBACF,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CACvC,UAAU,CAAC,oBAAoB,CAC7B,IAAI,CAAC,MAAM,EACX,iBAAiB,EACjB,IAAI,CAAC,mBAAmB,CACzB,EACD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,CAAC,CACtE,CAAC;gBAEF,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAC7C,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAC1B,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3B,CAAC;gBAEF,OAAO,GAAG,UAAU,GAAG,aAAa,EAAE,CAAC;aACxC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACvC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAC9C;gBACA,OAAO,IAAI,CAAC,IAAI,CAAC;aAClB;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE;gBAC7C,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;aAClD;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAC/C,OAAO,MAAM,CAAC;aACf;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAChD,wBAAwB,CAAC,IACvB,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAC3D;oBACA,wBAAwB;oBACxB,OAAO,EAAE,CAAC;iBACX;gBACD,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACjC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;gBAC3D,gDAAgD;gBAChD,OAAO,EAAE,CAAC;aACX;YAED,OAAO,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QAED;;WAEG;QACH,SAAS,uBAAuB,CAAC,IAA+B;YAC9D,IAAI,UAAkB,CAAC;YAEvB,6DAA6D;YAC7D,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBACxB,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,UAAU,GAAG,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAClD,MAAM;gBAER,KAAK,sBAAc,CAAC,cAAc,CAAC;gBACnC,KAAK,sBAAc,CAAC,UAAU,CAAC;gBAC/B,KAAK,sBAAc,CAAC,YAAY,CAAC;gBACjC,KAAK,sBAAc,CAAC,cAAc;oBAChC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAClC,MAAM;gBAER,0BAA0B;gBAC1B;oBACE,OAAO,EAAE,CAAC;aACb;YAED,IAAI,YAAoB,CAAC;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,6DAA6D;gBAC7D,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC1B,KAAK,sBAAc,CAAC,UAAU;wBAC5B,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACtC,MAAM;oBAER,KAAK,sBAAc,CAAC,OAAO,CAAC;oBAC5B,KAAK,sBAAc,CAAC,eAAe,CAAC;oBACpC,KAAK,sBAAc,CAAC,gBAAgB;wBAClC,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACjD,MAAM;oBAER,KAAK,sBAAc,CAAC,gBAAgB;wBAClC,YAAY,GAAG,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACtD,MAAM;oBAER,0BAA0B;oBAC1B;wBACE,OAAO,EAAE,CAAC;iBACb;gBAED,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,YAAY,GAAG,CAAC;aACrE;iBAAM;gBACL,iEAAiE;gBACjE,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;oBAC1B,KAAK,sBAAc,CAAC,UAAU;wBAC5B,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACtC,MAAM;oBACR,KAAK,sBAAc,CAAC,iBAAiB;wBACnC,YAAY,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC5C,MAAM;oBAER;wBACE,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACpD;gBAED,OAAO,GAAG,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,YAAY,EAAE,CAAC;aACpE;QACH,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,MAAM,2BAA2B,GAAgC,IAAI,GAAG,CAAC;IACvE,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,cAAc;IAC7B,sBAAc,CAAC,YAAY;CAC5B,CAAC,CAAC;AACH,MAAM,2BAA2B,GAAgC,IAAI,GAAG,CAAC;IACvE,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,OAAO;IACtB,sBAAc,CAAC,gBAAgB;IAC/B,sBAAc,CAAC,eAAe;CAC/B,CAAC,CAAC;AACH,MAAM,+BAA+B,GAAgC,IAAI,GAAG,CAAC;IAC3E,sBAAc,CAAC,UAAU;IACzB,sBAAc,CAAC,iBAAiB;CACjC,CAAC,CAAC;AAgBH,SAAS,mBAAmB,CAAC,EAC3B,eAAe,EACf,QAAQ,EACR,qBAAqB,EACrB,UAAU,EACV,OAAO,EACP,uBAAuB,GACI;IAC3B,IAAI,eAAe,GAAG,CAAC,EAAE;QACvB,IACE,uBAAuB;YACvB,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACvD;YACA,IAAI,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;YACvC,IACE,QAAQ,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK;gBACjC,mEAAmE;gBACnE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACpD,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,MAAM,EACnC;gBACA,6CAA6C;gBAC7C,QAAQ,GAAG,IAAI,CAAC;aACjB;YACD,yCAAyC;YACzC,qBAAqB,IAAI,IAAI,QAAQ,IAAI,UAAU,CAAC,OAAO,CACzD,QAAQ,CAAC,KAAK,CAAC,KAAK,CACrB,EAAE,CAAC;SACL;QAED,OAAO,CAAC,MAAM,CAAC;YACb,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,qBAAqB;YAChC,OAAO,EAAE;gBACP;oBACE,SAAS,EAAE,sBAAsB;oBACjC,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;wBAClC,KAAK,CAAC,WAAW,CACf,QAAQ,EACR,GAAG,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,qBAAqB,EAAE,CAChE;qBACF;iBACF;aACF;SACF,CAAC,CAAC;KACJ;AACH,CAAC;AAWD,SAAS,0BAA0B,CACjC,SAAiB,EACjB,eAAuB,EACvB,gBAAwB,EACxB,qBAA6B,EAC7B,QAAuB,EACvB,OAAsB;IAEtB,MAAM,QAAQ,GAAG,gBAAgB,CAAC;IAClC,IAAI,uCAAuC,GAAG,KAAK,CAAC;IACpD,8EAA8E;IAC9E,IAAI,SAAS,KAAK,gBAAgB,EAAE;QAClC,eAAe,IAAI,CAAC,CAAC;QACrB,gBAAgB,GAAG,SAAS,CAAC;QAE7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;UA4BE;QACF,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACzB,kJAAkJ;YAClJ,wCAAwC;YACxC,uCAAuC,GAAG,IAAI,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,kCAAkC;YAClC,qBAAqB,IAAI,IAAI,CAAC;SAC/B;aAAM;YACL,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC9D,qBAAqB,IAAI,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC;SAC3D;KACF;IAED,QAAQ,GAAG,OAAqC,CAAC;IACjD,OAAO,GAAG,IAAI,CAAC,UAAU,CACvB,OAAO,CAAC,MAAM,EACd,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACrC,CAAC;IACF,OAAO;QACL,uCAAuC;QACvC,eAAe;QACf,gBAAgB;QAChB,qBAAqB;QACrB,QAAQ;QACR,OAAO;KACR,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,IAAmB,EACnB,eAAwB;IAExB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;QAChD,OAAO,kBAAkB,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;KAC7D;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;QACjD,MAAM,aAAa,GACjB,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjD,oEAAoE;YACpE,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ;YACnC,CAAC,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACnD,2EAA2E;gBAC3E,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACrD,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC;oBACpD,CAAC,CAAC,IAAI,CAAC;YACX,CAAC,CAAC,+BAA+B,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE5D,OAAO,aAAa,IAAI,eAAe,CAAC;KACzC;IAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;QAC/C,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;KACzD;IAED,IACE,eAAe;QACf,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACtC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;YAC3C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,CAAC,EAC5C;QACA,OAAO,IAAI,CAAC;KACb;IAED;;;;;;MAME;IACF,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC7C,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;QACrC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC;QAC9C,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAC3E,CAAC;AACJ,CAAC"}