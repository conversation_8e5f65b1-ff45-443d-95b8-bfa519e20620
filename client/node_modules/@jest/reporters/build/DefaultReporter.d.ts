/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/// <reference types="node" />
import type { AggregatedResult, TestCaseResult, TestResult } from '@jest/test-result';
import type { Config } from '@jest/types';
import BaseReporter from './BaseReporter';
import type { ReporterOnStartOptions, Test } from './types';
export default class DefaultReporter extends BaseReporter {
    private _clear;
    private _err;
    protected _globalConfig: Config.GlobalConfig;
    private _out;
    private _status;
    private _bufferedOutput;
    static readonly filename: string;
    constructor(globalConfig: Config.GlobalConfig);
    protected __wrapStdio(stream: NodeJS.WritableStream | NodeJS.WriteStream): void;
    forceFlushBufferedOutput(): void;
    protected __clearStatus(): void;
    protected __printStatus(): void;
    onRunStart(aggregatedResults: AggregatedResult, options: ReporterOnStartOptions): void;
    onTestStart(test: Test): void;
    onTestCaseResult(test: Test, testCaseResult: TestCaseResult): void;
    onRunComplete(): void;
    onTestResult(test: Test, testResult: TestResult, aggregatedResults: AggregatedResult): void;
    testFinished(config: Config.ProjectConfig, testResult: TestResult, aggregatedResults: AggregatedResult): void;
    printTestFileHeader(_testPath: Config.Path, config: Config.ProjectConfig, result: TestResult): void;
    printTestFileFailureMessage(_testPath: Config.Path, _config: Config.ProjectConfig, result: TestResult): void;
}
