{"name": "@rollup/plugin-replace", "version": "2.4.2", "publishConfig": {"access": "public"}, "description": "Replace strings in files while bundling", "license": "MIT", "repository": "rollup/plugins", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/replace#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "dist/rollup-plugin-replace.cjs.js", "module": "dist/rollup-plugin-replace.es.js", "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm run test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm run build && pnpm run lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm run test -- --verbose && pnpm run test:ts", "lint": "pnpm run lint:js && pnpm run lint:docs && pnpm run lint:package", "lint:docs": "prettier --single-quote --arrow-parens avoid --trailing-comma none --write README.md", "lint:js": "eslint --fix --cache src test types --ext .js,.ts", "lint:package": "prettier --write package.json --plugin=prettier-plugin-package", "prebuild": "del-cli dist", "prepare": "pnpm run build", "prepublishOnly": "pnpm run lint && pnpm run test", "pretest": "pnpm run build", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}, "files": ["dist", "src", "types", "README.md"], "keywords": ["rollup", "plugin", "replace", "es2015", "npm", "modules"], "peerDependencies": {"rollup": "^1.20.0 || ^2.0.0"}, "dependencies": {"@rollup/pluginutils": "^3.1.0", "magic-string": "^0.25.7"}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "del-cli": "^3.0.1", "locate-character": "^2.0.5", "rollup": "^2.23.0", "source-map": "^0.7.3", "typescript": "^3.9.7"}, "types": "types/index.d.ts", "ava": {"babel": {"compileEnhancements": false}, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}}